"""
Configuration settings for the Expedition Planner application.
Legacy configuration - use config_manager for new code.
"""

import os
from pathlib import Path
from typing import Any, Dict

try:
    from .config.config_manager import get_config as get_centralized_config
except ImportError:
    # Fallback for when relative imports don't work
    try:
        from expedition_planner.config.config_manager import (
            get_config as get_centralized_config,
        )
    except ImportError:
        # If config manager is not available, create a dummy function
        def get_centralized_config():
            from dataclasses import dataclass

            @dataclass
            class DummyConfig:
                def to_dict(self):
                    return {}

            return DummyConfig()


# Base directories
BASE_DIR = Path(__file__).parent
PROJECT_ROOT = BASE_DIR.parent
TEMPLATES_DIR = BASE_DIR / "templates"
UPLOADS_DIR = BASE_DIR / "uploads"
OUTPUTS_DIR = PROJECT_ROOT / "consolidated_outputs"

# Ensure directories exist
UPLOADS_DIR.mkdir(exist_ok=True)
OUTPUTS_DIR.mkdir(exist_ok=True)

# Ollama configuration
OLLAMA_CONFIG = {
    "base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
    "model": os.getenv("OLLAMA_MODEL", "mistral-7b-v0-1-gguf:latest"),
    "timeout": int(os.getenv("OLLAMA_TIMEOUT", "300")),
    "temperature": float(
        os.getenv("OLLAMA_TEMPERATURE", "0.1")
    ),  # Low temperature for consistent extraction
}

# Document processing configuration
DOCLING_CONFIG = {
    "supported_formats": [
        ".pdf",
        ".docx",
        ".pptx",
        ".jpg",
        ".jpeg",
        ".png",
        ".html",
        ".md",
    ],
    "max_file_size": int(
        os.getenv("MAX_FILE_SIZE", str(50 * 1024 * 1024))
    ),  # 50MB default
    "batch_size": int(os.getenv("BATCH_SIZE", "10")),
}

# Data extraction patterns
EXTRACTION_PATTERNS = {
    "coordinates": [
        r"(\d{1,3}\.\d+)[°]?\s*[NS],?\s*(\d{1,3}\.\d+)[°]?\s*[EW]",
        r"(\d{1,3}°\d{1,2}'\d{1,2}\"[NS]),?\s*(\d{1,3}°\d{1,2}'\d{1,2}\"[EW])",
        r"lat[itude]*:?\s*(-?\d+\.?\d*),?\s*lon[gitude]*:?\s*(-?\d+\.?\d*)",
    ],
    "personnel_count": [
        r"(\d+)\s*(?:people|persons|members|team|crew|staff)",
        r"team\s*(?:of|size)?\s*(\d+)",
        r"(\d+)\s*expedition\s*members",
    ],
    "equipment": [
        r"equipment:?\s*(.+?)(?:\n|$)",
        r"gear:?\s*(.+?)(?:\n|$)",
        r"supplies:?\s*(.+?)(?:\n|$)",
    ],
    "safety_incidents": [
        r"incident:?\s*(.+?)(?:\n|$)",
        r"accident:?\s*(.+?)(?:\n|$)",
        r"injury:?\s*(.+?)(?:\n|$)",
        r"emergency:?\s*(.+?)(?:\n|$)",
    ],
}

# Statistical analysis configuration
ANALYSIS_CONFIG = {
    "anomaly_threshold": 2.0,  # Standard deviations for anomaly detection
    "trend_window": 3,  # Days for trend analysis
    "confidence_level": 0.95,
}

# Report generation configuration
REPORT_CONFIG = {
    "formats": ["pdf", "excel", "json"],
    "template_engine": "jinja2",
    "include_charts": True,
    "chart_style": "seaborn",
}

# Web interface configuration
WEB_CONFIG = {
    "host": os.getenv("WEB_HOST", "localhost"),
    "port": int(os.getenv("WEB_PORT", "8080")),
    "debug": os.getenv("WEB_DEBUG", "true").lower() == "true",
    "upload_folder": str(UPLOADS_DIR),
    "max_content_length": int(
        os.getenv("MAX_FILE_SIZE", str(100 * 1024 * 1024))
    ),  # 100MB default
}


def get_config() -> Dict[str, Any]:
    """
    Get the complete configuration dictionary.

    This function now uses the centralized configuration manager
    but maintains backward compatibility with the legacy format.
    """
    # Get centralized config
    centralized_config = get_centralized_config()

    # Convert to legacy format for backward compatibility
    return {
        "ollama": OLLAMA_CONFIG,
        "docling": DOCLING_CONFIG,
        "extraction": EXTRACTION_PATTERNS,
        "analysis": ANALYSIS_CONFIG,
        "report": REPORT_CONFIG,
        "web": WEB_CONFIG,
        "directories": {
            "base": str(BASE_DIR),
            "templates": str(TEMPLATES_DIR),
            "uploads": str(UPLOADS_DIR),
            "outputs": str(OUTPUTS_DIR),
        },
        # Add new centralized config
        "centralized": centralized_config.to_dict(),
    }
