"""
Standardized error handling for expedition planner API.
Provides consistent error responses and logging.
"""

import logging
import traceback
from datetime import datetime
from functools import wraps
from typing import Any, Dict, Optional, Tuple

from flask import jsonify, request

logger = logging.getLogger(__name__)


class APIError(Exception):
    """Base API error class."""
    
    def __init__(self, message: str, status_code: int = 500, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        self.timestamp = datetime.now().isoformat()


class ValidationError(APIError):
    """Validation error."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 400, details)


class NotFoundError(APIError):
    """Resource not found error."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 404, details)


class ProcessingError(APIError):
    """Document processing error."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 422, details)


class ServerError(APIError):
    """Internal server error."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 500, details)


def format_error_response(error: APIError) -> Tuple[Dict[str, Any], int]:
    """Format error response for API."""
    response = {
        "success": False,
        "error": {
            "message": error.message,
            "type": error.__class__.__name__,
            "timestamp": error.timestamp,
            "status_code": error.status_code
        }
    }
    
    # Add details if available
    if error.details:
        response["error"]["details"] = error.details
    
    # Add request context for debugging
    if request:
        response["error"]["request"] = {
            "method": request.method,
            "path": request.path,
            "remote_addr": request.remote_addr
        }
    
    return response, error.status_code


def handle_api_error(func):
    """Decorator for standardized API error handling."""
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except APIError as e:
            # Known API errors - log and return formatted response
            logger.warning(f"API error in {func.__name__}: {e.message}")
            return format_error_response(e)
        except Exception as e:
            # Unexpected errors - log with full traceback and return generic error
            logger.error(f"Unexpected error in {func.__name__}: {e}")
            logger.error(traceback.format_exc())
            
            server_error = ServerError(
                "An unexpected error occurred",
                details={"original_error": str(e), "function": func.__name__}
            )
            return format_error_response(server_error)
    
    return wrapper


def validate_request_data(required_fields: list, optional_fields: list = None) -> Dict[str, Any]:
    """Validate request data and return cleaned data."""
    if not request.is_json:
        raise ValidationError("Request must be JSON")
    
    data = request.get_json()
    if not data:
        raise ValidationError("Request body is empty")
    
    # Check required fields
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None:
            missing_fields.append(field)
    
    if missing_fields:
        raise ValidationError(
            f"Missing required fields: {', '.join(missing_fields)}",
            details={"missing_fields": missing_fields}
        )
    
    # Extract and validate fields
    validated_data = {}
    all_fields = required_fields + (optional_fields or [])
    
    for field in all_fields:
        if field in data:
            validated_data[field] = data[field]
    
    return validated_data


def validate_file_upload(files, max_files: int = 10, max_size_mb: int = 50) -> list:
    """Validate file upload and return valid files."""
    if not files:
        raise ValidationError("No files provided")
    
    file_list = files.getlist("files") if hasattr(files, 'getlist') else [files]
    
    if len(file_list) > max_files:
        raise ValidationError(
            f"Too many files. Maximum {max_files} files allowed",
            details={"max_files": max_files, "provided_files": len(file_list)}
        )
    
    valid_files = []
    errors = []
    
    allowed_extensions = {'.pdf', '.docx', '.doc', '.txt', '.md', '.png', '.jpg', '.jpeg', '.tiff', '.bmp'}
    max_size_bytes = max_size_mb * 1024 * 1024
    
    for file in file_list:
        if not file.filename:
            continue
        
        # Check file extension
        file_ext = '.' + file.filename.split('.')[-1].lower()
        if file_ext not in allowed_extensions:
            errors.append(f"{file.filename}: Unsupported file type ({file_ext})")
            continue
        
        # Check file size
        file.seek(0, 2)  # Seek to end
        file_size = file.tell()
        file.seek(0)  # Reset to beginning
        
        if file_size > max_size_bytes:
            errors.append(f"{file.filename}: File too large ({file_size / 1024 / 1024:.1f}MB > {max_size_mb}MB)")
            continue
        
        if file_size == 0:
            errors.append(f"{file.filename}: File is empty")
            continue
        
        valid_files.append(file)
    
    if errors and not valid_files:
        raise ValidationError(
            "No valid files found",
            details={"errors": errors, "allowed_extensions": list(allowed_extensions)}
        )
    
    if errors:
        logger.warning(f"File validation warnings: {errors}")
    
    return valid_files


def create_success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """Create standardized success response."""
    response = {
        "success": True,
        "message": message,
        "timestamp": datetime.now().isoformat()
    }
    
    if data is not None:
        response["data"] = data
    
    return response


def log_api_request(func):
    """Decorator to log API requests."""
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = datetime.now()
        
        # Log request
        logger.info(f"API Request: {request.method} {request.path} from {request.remote_addr}")
        
        try:
            result = func(*args, **kwargs)
            
            # Log successful response
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"API Response: {request.method} {request.path} completed in {duration:.3f}s")
            
            return result
            
        except Exception as e:
            # Log error
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"API Error: {request.method} {request.path} failed in {duration:.3f}s - {str(e)}")
            raise
    
    return wrapper


class ErrorTracker:
    """Track and analyze API errors."""
    
    def __init__(self):
        self.error_history = []
        self.max_history = 1000
    
    def record_error(self, error: APIError, request_info: Dict[str, Any]):
        """Record an error for analysis."""
        error_record = {
            "timestamp": error.timestamp,
            "error_type": error.__class__.__name__,
            "message": error.message,
            "status_code": error.status_code,
            "request": request_info,
            "details": error.details
        }
        
        self.error_history.append(error_record)
        
        # Keep only recent errors
        if len(self.error_history) > self.max_history:
            self.error_history = self.error_history[-self.max_history:]
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of recent errors."""
        if not self.error_history:
            return {"total_errors": 0, "by_type": {}, "recent_errors": []}
        
        # Count by error type
        error_counts = {}
        for error in self.error_history:
            error_type = error["error_type"]
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        # Get recent errors (last 10)
        recent_errors = self.error_history[-10:]
        
        return {
            "total_errors": len(self.error_history),
            "by_type": error_counts,
            "recent_errors": recent_errors,
            "last_error_time": self.error_history[-1]["timestamp"] if self.error_history else None
        }


# Global error tracker instance
error_tracker = ErrorTracker()
