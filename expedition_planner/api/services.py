"""
Business logic services for expedition planner API.
Separates business logic from web routes.
"""

import json
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from werkzeug.utils import secure_filename

from ..core.text_extractor import TextExtractor
from ..tools.template_generator import TemplateGeneratorTool
from ..utils.cleanup_manager import ComprehensiveCleanupManager

logger = logging.getLogger(__name__)


class SessionService:
    """Service for managing user sessions."""
    
    def __init__(self):
        self.active_sessions = {}
        self.session_timeout_hours = 24
        self.max_active_sessions = 100
    
    def create_session(self, upload_dir: Path) -> Tuple[str, Path]:
        """Create a new session and return session ID and directory."""
        session_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_dir = upload_dir / f"{session_id}_{timestamp}"
        session_dir.mkdir(exist_ok=True)
        
        self.active_sessions[session_id] = {
            "session_id": session_id,
            "upload_time": datetime.now(),
            "session_dir": str(session_dir),
            "status": "created",
            "files": []
        }
        
        return session_id, session_dir
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data by ID."""
        return self.active_sessions.get(session_id)
    
    def update_session(self, session_id: str, **kwargs) -> bool:
        """Update session data."""
        if session_id in self.active_sessions:
            self.active_sessions[session_id].update(kwargs)
            return True
        return False
    
    def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions."""
        current_time = datetime.now()
        expired_sessions = []
        
        for session_id, session_data in self.active_sessions.items():
            session_time = session_data.get('upload_time', current_time)
            if isinstance(session_time, str):
                try:
                    session_time = datetime.fromisoformat(session_time)
                except ValueError:
                    session_time = current_time
            
            time_diff = current_time - session_time
            if time_diff.total_seconds() > (self.session_timeout_hours * 3600):
                expired_sessions.append(session_id)
        
        # Remove expired sessions
        for session_id in expired_sessions:
            try:
                session_data = self.active_sessions.pop(session_id, {})
                logger.info(f"Cleaned up expired session: {session_id}")
                
                # Clean up session directory
                session_dir = session_data.get('session_dir')
                if session_dir and Path(session_dir).exists():
                    import shutil
                    shutil.rmtree(session_dir, ignore_errors=True)
            except Exception as e:
                logger.warning(f"Error cleaning up session {session_id}: {e}")
        
        return len(expired_sessions)
    
    def enforce_session_limits(self) -> int:
        """Enforce maximum number of active sessions."""
        if len(self.active_sessions) <= self.max_active_sessions:
            return 0
        
        # Sort sessions by upload time (oldest first)
        sorted_sessions = sorted(
            self.active_sessions.items(),
            key=lambda x: x[1].get('upload_time', datetime.min)
        )
        
        # Remove oldest sessions
        sessions_to_remove = len(self.active_sessions) - self.max_active_sessions
        removed_count = 0
        
        for session_id, session_data in sorted_sessions[:sessions_to_remove]:
            try:
                self.active_sessions.pop(session_id, None)
                logger.info(f"Removed session due to limit: {session_id}")
                
                # Clean up session directory
                session_dir = session_data.get('session_dir')
                if session_dir and Path(session_dir).exists():
                    import shutil
                    shutil.rmtree(session_dir, ignore_errors=True)
                
                removed_count += 1
            except Exception as e:
                logger.warning(f"Error removing session {session_id}: {e}")
        
        return removed_count


class FileService:
    """Service for file operations."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.allowed_extensions = {'.pdf', '.docx', '.doc', '.txt', '.md', '.png', '.jpg', '.jpeg', '.tiff', '.bmp'}
        self.max_file_size = 50 * 1024 * 1024  # 50MB
    
    def validate_files(self, files) -> Tuple[List, List[str]]:
        """Validate uploaded files and return valid files and errors."""
        valid_files = []
        errors = []
        
        for file in files:
            if not file.filename:
                continue
                
            file_ext = Path(file.filename).suffix.lower()
            if file_ext not in self.allowed_extensions:
                errors.append(f"{file.filename}: Unsupported file type ({file_ext})")
                continue
            
            # Check file size
            file.seek(0, 2)  # Seek to end
            file_size = file.tell()
            file.seek(0)  # Reset to beginning
            
            if file_size > self.max_file_size:
                errors.append(f"{file.filename}: File too large ({file_size / 1024 / 1024:.1f}MB > 50MB)")
                continue
            
            if file_size == 0:
                errors.append(f"{file.filename}: File is empty")
                continue
            
            valid_files.append(file)
        
        return valid_files, errors
    
    def save_files(self, files: List, session_dir: Path) -> List[Dict[str, Any]]:
        """Save files to session directory."""
        uploaded_files = []
        
        for file in files:
            if file and file.filename:
                filename = secure_filename(file.filename)
                file_path = session_dir / filename
                
                try:
                    file.save(str(file_path))
                    uploaded_files.append({
                        "name": filename,
                        "path": str(file_path),
                        "size": file_path.stat().st_size,
                        "type": Path(filename).suffix.lower(),
                    })
                except Exception as e:
                    logger.error(f"Error saving file {filename}: {e}")
                    raise
        
        return uploaded_files
    
    def resolve_file_path(self, filename: str) -> Optional[Path]:
        """Resolve file path with security validation."""
        try:
            import urllib.parse
            decoded_filename = urllib.parse.unquote(filename)
            
            # Define allowed directories
            allowed_dirs = [
                Path(self.config["directories"]["outputs"]),
                Path(self.config["directories"]["uploads"]),
                Path("consolidated_outputs"),
            ]
            
            # Ensure all allowed directories exist
            for allowed_dir in allowed_dirs:
                allowed_dir.mkdir(parents=True, exist_ok=True)
            
            # If it's an absolute path, validate it's in allowed directories
            if decoded_filename.startswith('/'):
                potential_path = Path(decoded_filename)
                for allowed_dir in allowed_dirs:
                    try:
                        potential_path.resolve().relative_to(allowed_dir.resolve())
                        if potential_path.exists() and potential_path.is_file():
                            return potential_path
                    except ValueError:
                        continue
            else:
                # For relative paths, try different base directories
                search_dirs = allowed_dirs + [Path.cwd()]
                
                for base_dir in search_dirs:
                    potential_path = base_dir / decoded_filename
                    if potential_path.exists() and potential_path.is_file():
                        # Verify it's still within allowed directories
                        for allowed_dir in allowed_dirs:
                            try:
                                potential_path.resolve().relative_to(allowed_dir.resolve())
                                return potential_path
                            except ValueError:
                                continue
            
            # If not found, try searching by filename only
            filename_only = Path(decoded_filename).name
            for base_dir in allowed_dirs:
                for json_file in base_dir.rglob(filename_only):
                    if json_file.is_file():
                        return json_file
            
            return None
            
        except Exception as e:
            logger.error(f"Error resolving file path for {filename}: {e}")
            return None
    
    def list_json_files(self) -> List[Dict[str, Any]]:
        """List all available JSON files."""
        json_files = []
        
        # Scan consolidated_outputs directory
        consolidated_dir = Path("consolidated_outputs")
        if consolidated_dir.exists():
            for json_file in consolidated_dir.glob("*.json"):
                if "workflow_state" not in json_file.name:
                    file_info = self._get_file_info(json_file)
                    if file_info:
                        json_files.append(file_info)
        
        # Scan output directories
        output_dir = Path(self.config["directories"]["outputs"])
        if output_dir.exists():
            for expedition_dir in output_dir.iterdir():
                if expedition_dir.is_dir():
                    for json_file in expedition_dir.glob("*.json"):
                        if "workflow_state" not in json_file.name:
                            file_info = self._get_file_info(json_file, expedition_dir.name)
                            if file_info:
                                json_files.append(file_info)
        
        # Sort by modification time (newest first)
        json_files.sort(key=lambda x: x["modified"], reverse=True)
        return json_files
    
    def _get_file_info(self, json_file: Path, expedition_name: str = None) -> Optional[Dict[str, Any]]:
        """Get file information for a JSON file."""
        try:
            # Try to extract expedition name from file content
            if not expedition_name:
                expedition_name = "Unknown"
                try:
                    with open(json_file, 'r') as f:
                        content = json.load(f)
                        if isinstance(content, dict):
                            for key in ['location', 'expedition', 'expedition_name']:
                                if key in content and content[key]:
                                    expedition_name = content[key]
                                    break
                except:
                    pass
            
            return {
                "path": str(json_file),
                "name": json_file.name,
                "expedition": expedition_name,
                "size": json_file.stat().st_size,
                "modified": datetime.fromtimestamp(json_file.stat().st_mtime).isoformat(),
            }
        except Exception as e:
            logger.warning(f"Error getting file info for {json_file}: {e}")
            return None


class DocumentService:
    """Service for document processing operations."""
    
    def __init__(self):
        self.text_extractor = TextExtractor()
        self.template_generator = TemplateGeneratorTool()
    
    def process_documents(self, documents_dir: str, expedition_name: str, output_dir: str) -> Dict[str, Any]:
        """Process documents and generate JSON templates."""
        try:
            logger.info(f"Starting document processing: {expedition_name}")
            
            # Get all document files
            doc_files = []
            for ext in ['.pdf', '.docx', '.doc', '.txt', '.md', '.png', '.jpg', '.jpeg', '.tiff', '.bmp']:
                doc_files.extend(Path(documents_dir).glob(f"*{ext}"))
            
            if not doc_files:
                return {
                    "success": False,
                    "error": "No supported document files found",
                    "status": "failed"
                }
            
            logger.info(f"Found {len(doc_files)} documents to process")
            
            # Process each document
            all_extracted_data = []
            extraction_stats = {
                "total_files": len(doc_files),
                "successful_extractions": 0,
                "failed_extractions": 0
            }
            
            for doc_file in doc_files:
                try:
                    logger.info(f"Processing document: {doc_file.name}")
                    
                    # Extract text from document
                    text_content = self.text_extractor.extract_text(str(doc_file))
                    
                    if not text_content or len(text_content.strip()) < 50:
                        logger.warning(f"Insufficient text extracted from {doc_file.name}")
                        extraction_stats["failed_extractions"] += 1
                        continue
                    
                    # Create structured data from text
                    structured_data = self._extract_structured_data_from_text(text_content, doc_file.name)
                    all_extracted_data.append(structured_data)
                    extraction_stats["successful_extractions"] += 1
                    
                except Exception as e:
                    logger.error(f"Error processing {doc_file.name}: {e}")
                    extraction_stats["failed_extractions"] += 1
                    continue
            
            if not all_extracted_data:
                return {
                    "success": False,
                    "error": "No data could be extracted from documents",
                    "status": "failed",
                    "extraction_stats": extraction_stats
                }
            
            # Generate JSON templates
            generated_files = []
            for i, data in enumerate(all_extracted_data):
                try:
                    # Ensure required fields
                    if "schedule" not in data or not data["schedule"]:
                        data["schedule"] = [
                            {"time": "08:00", "type": "arrival", "description": "Arrival at location"},
                            {"time": "12:00", "type": "departure", "description": "Departure from location"}
                        ]
                    
                    # Generate JSON template
                    json_data = json.dumps(data)
                    location = data.get("location", "Unknown_Location") or "Unknown_Location"
                    
                    file_path = self.template_generator._run(
                        extracted_data=json_data,
                        operation_type="combined",
                        location=location,
                        output_path=output_dir
                    )
                    
                    generated_files.append(file_path)
                    logger.info(f"Generated JSON file: {file_path}")
                    
                except Exception as e:
                    logger.error(f"Error generating JSON template {i}: {e}")
                    continue
            
            return {
                "success": True,
                "status": "completed",
                "message": f"Successfully processed {len(doc_files)} documents and generated {len(generated_files)} JSON files",
                "generated_files": generated_files,
                "extraction_stats": extraction_stats
            }
            
        except Exception as e:
            logger.error(f"Error in document processing: {e}")
            return {
                "success": False,
                "error": str(e),
                "status": "failed"
            }
    
    def _extract_structured_data_from_text(self, text_content: str, filename: str) -> Dict[str, Any]:
        """Extract structured data from text content."""
        import re
        
        # Initialize structure
        data = {
            "date": None,
            "location": None,
            "arrival_time": None,
            "departure_time": None,
            "operation_type": "combined",
            "groups": [],
            "schedule": [],
            "tides": [],
            "equipment": {"zodiacs": None, "twins": None, "other": []},
            "personnel": {"total_count": 0, "guides": [], "drivers": []},
            "weather": None,
            "notes": f"Extracted from: {filename}"
        }
        
        # Extract date
        date_patterns = [
            r'\b\d{4}-\d{2}-\d{2}\b',
            r'\b\d{2}/\d{2}/\d{4}\b',
            r'\b\d{1,2}/\d{1,2}/\d{4}\b',
            r'\b\d{1,2}-\d{1,2}-\d{4}\b',
            r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{4}\b',
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text_content, re.IGNORECASE)
            if match:
                try:
                    date_str = match.group(0)
                    if '/' in date_str or '-' in date_str:
                        parts = re.split('[/-]', date_str)
                        if len(parts) == 3:
                            if len(parts[0]) == 4:
                                data["date"] = f"{parts[0]}-{parts[1].zfill(2)}-{parts[2].zfill(2)}"
                            else:
                                data["date"] = f"{parts[2]}-{parts[0].zfill(2)}-{parts[1].zfill(2)}"
                    break
                except:
                    continue
        
        # Extract location
        location_patterns = [
            r'Location:\s*([^\n\r]+)',
            r'([A-Z][a-z]+\s+[A-Z][a-z]+\s+(?:River|Island|Bay|Reef|Falls))',
            r'([A-Z][a-z]+\s+(?:River|Island|Bay|Reef|Falls))'
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, text_content, re.IGNORECASE)
            if match:
                location = match.group(1).strip()
                if len(location) > 3:
                    data["location"] = location
                    break
        
        # Extract schedule
        time_patterns = r'(\d{1,2}:\d{2})\s*[-–]\s*([^\n\r]+)'
        times = re.findall(time_patterns, text_content)
        
        for time_match in times[:10]:
            time_str, description = time_match
            data["schedule"].append({
                "time": time_str,
                "type": "activity",
                "description": description.strip()[:100],
                "location": data["location"]
            })
        
        return data
