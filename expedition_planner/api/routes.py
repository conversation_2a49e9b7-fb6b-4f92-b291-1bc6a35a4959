"""
API routes for expedition planner.
Provides clean separation between business logic and web layer.
"""

import logging
from flask import Blueprint, request, jsonify, send_file
from flask_socketio import emit, join_room

from .error_handlers import (
    handle_api_error, 
    log_api_request, 
    validate_file_upload, 
    create_success_response,
    ValidationError,
    NotFoundError,
    ProcessingError
)
from .services import DocumentService, SessionService, FileService
from ..config.app_config import get_config

logger = logging.getLogger(__name__)


def create_api_blueprint(socketio=None) -> Blueprint:
    """Create API blueprint with all routes."""
    
    api = Blueprint('api', __name__, url_prefix='/api')
    config = get_config()
    
    # Initialize services
    session_service = SessionService()
    file_service = FileService(config.to_dict())
    document_service = DocumentService()
    
    @api.route('/health', methods=['GET'])
    @log_api_request
    @handle_api_error
    def health_check():
        """Health check endpoint."""
        return create_success_response({
            "status": "healthy",
            "version": "2.0.0",
            "services": {
                "document_processing": "available",
                "file_management": "available",
                "session_management": "available"
            }
        })
    
    @api.route('/upload-documents', methods=['POST'])
    @log_api_request
    @handle_api_error
    def upload_documents():
        """Handle document upload for processing."""
        # Clean up expired sessions
        session_service.cleanup_expired_sessions()
        session_service.enforce_session_limits()
        
        if "files" not in request.files:
            raise ValidationError("No files provided")
        
        # Validate files
        files = validate_file_upload(
            request.files,
            max_files=config.files.max_files_per_upload,
            max_size_mb=config.files.max_file_size_mb
        )
        
        # Create session
        session_id, session_dir = session_service.create_session(
            Path(config.directories.uploads)
        )
        
        try:
            # Save files
            uploaded_files = file_service.save_files(files, session_dir)
            
            # Update session
            session_service.update_session(
                session_id,
                files=uploaded_files,
                status="uploaded"
            )
            
            return create_success_response({
                "session_id": session_id,
                "files": uploaded_files,
                "message": f"Successfully uploaded {len(uploaded_files)} files"
            })
            
        except Exception as e:
            logger.error(f"Error saving files for session {session_id}: {e}")
            raise ProcessingError(f"Failed to save files: {str(e)}")
    
    @api.route('/process-documents', methods=['POST'])
    @log_api_request
    @handle_api_error
    def process_documents():
        """Process uploaded documents."""
        data = request.get_json()
        
        if not data:
            raise ValidationError("Request body is required")
        
        session_id = data.get('session_id')
        expedition_name = data.get('expedition_name')
        enable_analysis = data.get('enable_analysis', False)
        
        if not session_id:
            raise ValidationError("Session ID is required")
        
        if not expedition_name:
            raise ValidationError("Expedition name is required")
        
        # Get session
        session = session_service.get_session(session_id)
        if not session:
            raise NotFoundError("Session not found")
        
        # Join socket room for progress updates
        if socketio and socketio.server:
            join_room(session_id)
        
        try:
            # Process documents
            result = document_service.process_documents(
                documents_dir=session['session_dir'],
                expedition_name=expedition_name,
                output_dir=config.directories.outputs
            )
            
            if result['success']:
                # Update session
                session_service.update_session(
                    session_id,
                    status="completed",
                    result=result
                )
                
                # Emit completion event
                if socketio:
                    socketio.emit('processing_complete', result, room=session_id)
                
                return create_success_response(result)
            else:
                raise ProcessingError(result.get('error', 'Processing failed'))
                
        except Exception as e:
            logger.error(f"Error processing documents for session {session_id}: {e}")
            
            # Update session with error
            session_service.update_session(
                session_id,
                status="error",
                error=str(e)
            )
            
            # Emit error event
            if socketio:
                socketio.emit('error', {'message': str(e)}, room=session_id)
            
            raise ProcessingError(f"Document processing failed: {str(e)}")
    
    @api.route('/list-json-files', methods=['GET'])
    @log_api_request
    @handle_api_error
    def list_json_files():
        """List available JSON files."""
        try:
            json_files = file_service.list_json_files()
            
            return create_success_response({
                "json_files": json_files,
                "total_count": len(json_files)
            })
            
        except Exception as e:
            logger.error(f"Error listing JSON files: {e}")
            raise ProcessingError(f"Failed to list JSON files: {str(e)}")
    
    @api.route('/download/<path:filename>', methods=['GET'])
    @log_api_request
    @handle_api_error
    def download_file(filename):
        """Download file with secure path handling."""
        file_path = file_service.resolve_file_path(filename)
        
        if not file_path:
            raise NotFoundError(f"File not found: {filename}")
        
        try:
            # Check if JSON content is requested
            if request.args.get('format') == 'json' or request.headers.get('Accept') == 'application/json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return jsonify(json.loads(content))
            else:
                return send_file(str(file_path), as_attachment=True, download_name=file_path.name)
                
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in file {filename}: {e}")
            raise ValidationError("Invalid JSON file")
        except Exception as e:
            logger.error(f"Error downloading file {filename}: {e}")
            raise ProcessingError(f"Failed to download file: {str(e)}")
    
    @api.route('/view-json/<path:filename>', methods=['GET'])
    @log_api_request
    @handle_api_error
    def view_json_file(filename):
        """View JSON file content."""
        file_path = file_service.resolve_file_path(filename)
        
        if not file_path:
            raise NotFoundError(f"File not found: {filename}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return jsonify(json.loads(content))
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in file {filename}: {e}")
            raise ValidationError("Invalid JSON file")
        except Exception as e:
            logger.error(f"Error viewing JSON file {filename}: {e}")
            raise ProcessingError(f"Failed to view file: {str(e)}")
    
    @api.route('/analyze-patterns', methods=['POST'])
    @log_api_request
    @handle_api_error
    def analyze_patterns():
        """Analyze patterns in selected JSON files."""
        data = request.get_json()
        
        if not data:
            raise ValidationError("Request body is required")
        
        selected_files = data.get('selected_files', [])
        
        if len(selected_files) < 2:
            raise ValidationError(
                "Pattern analysis requires at least 2 JSON files",
                details={
                    "minimum_files": 2,
                    "provided_files": len(selected_files),
                    "user_guidance": {
                        "recommendation": "Select multiple expedition operation files to discover patterns",
                        "next_steps": [
                            "Process more expedition documents to generate additional JSON files",
                            "Select files from different locations or time periods",
                            "Ensure files contain operational data (not just metadata)"
                        ]
                    }
                }
            )
        
        try:
            # Resolve file paths
            json_file_paths = []
            for filename in selected_files:
                file_path = file_service.resolve_file_path(filename)
                if file_path:
                    json_file_paths.append(str(file_path))
                else:
                    logger.warning(f"Could not resolve file path: {filename}")
            
            if len(json_file_paths) < 2:
                raise ValidationError("Could not resolve enough valid file paths for analysis")
            
            # TODO: Implement pattern analysis service
            # For now, return a placeholder response
            analysis_result = {
                "patterns_found": 0,
                "analyzed_files": len(json_file_paths),
                "message": "Pattern analysis feature is under development"
            }
            
            return create_success_response(analysis_result)
            
        except Exception as e:
            logger.error(f"Error in pattern analysis: {e}")
            raise ProcessingError(f"Pattern analysis failed: {str(e)}")
    
    @api.route('/sessions/<session_id>', methods=['GET'])
    @log_api_request
    @handle_api_error
    def get_session(session_id):
        """Get session information."""
        session = session_service.get_session(session_id)
        
        if not session:
            raise NotFoundError("Session not found")
        
        return create_success_response(session)
    
    @api.route('/cleanup', methods=['POST'])
    @log_api_request
    @handle_api_error
    def cleanup():
        """Perform cleanup operations."""
        expired_count = session_service.cleanup_expired_sessions()
        removed_count = session_service.enforce_session_limits()
        
        return create_success_response({
            "expired_sessions_cleaned": expired_count,
            "sessions_removed_due_to_limits": removed_count,
            "message": "Cleanup completed successfully"
        })
    
    return api
