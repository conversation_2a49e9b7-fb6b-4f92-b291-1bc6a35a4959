/**
 * Expedition Report Converter Main JavaScript
 * Handles file uploads, drag & drop, progress tracking, and UI interactions
 */

class ExpeditionReportConverter {
    constructor() {
        this.uploadedFiles = [];
        this.maxFileSize = 50 * 1024 * 1024; // 50MB
        this.allowedTypes = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'image/jpeg',
            'image/png',
            'image/tiff',
            'text/plain',
            'text/markdown'
        ];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupDragAndDrop();
        this.setupFileValidation();
        this.setupProgressTracking();
    }
    
    setupEventListeners() {
        // File input change
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }
        
        // Upload area click
        const uploadArea = document.querySelector('.upload-area');
        if (uploadArea) {
            uploadArea.addEventListener('click', () => {
                if (fileInput) fileInput.click();
            });
        }
        
        // Process expedition button
        const processBtn = document.getElementById('process-expedition');
        if (processBtn) {
            processBtn.addEventListener('click', (e) => this.processExpedition(e));
        }
        
        // Remove file buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-file')) {
                this.removeFile(e.target.dataset.fileIndex);
            }
        });
    }
    
    setupDragAndDrop() {
        const uploadArea = document.querySelector('.upload-area');
        if (!uploadArea) return;
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => this.highlight(uploadArea), false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => this.unhighlight(uploadArea), false);
        });
        
        uploadArea.addEventListener('drop', (e) => this.handleDrop(e), false);
    }
    
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    highlight(element) {
        element.classList.add('dragover');
    }
    
    unhighlight(element) {
        element.classList.remove('dragover');
    }
    
    handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        this.handleFiles(files);
    }
    
    handleFileSelect(e) {
        const files = e.target.files;
        this.handleFiles(files);
    }
    
    handleFiles(files) {
        [...files].forEach(file => this.addFile(file));
        this.updateFileList();
        this.updateProcessButton();
    }
    
    addFile(file) {
        // Validate file
        const validation = this.validateFile(file);
        if (!validation.valid) {
            this.showError(`File "${file.name}": ${validation.error}`);
            return;
        }
        
        // Check for duplicates
        const isDuplicate = this.uploadedFiles.some(f => 
            f.name === file.name && f.size === file.size
        );
        
        if (isDuplicate) {
            this.showWarning(`File "${file.name}" is already added`);
            return;
        }
        
        // Add file with metadata
        const fileObj = {
            file: file,
            name: file.name,
            size: file.size,
            type: file.type,
            id: Date.now() + Math.random(),
            status: 'ready'
        };
        
        this.uploadedFiles.push(fileObj);
        this.showSuccess(`Added "${file.name}"`);
    }
    
    validateFile(file) {
        // Check file size
        if (file.size > this.maxFileSize) {
            return {
                valid: false,
                error: `File too large (${this.formatFileSize(file.size)}). Maximum size is ${this.formatFileSize(this.maxFileSize)}`
            };
        }

        // Check file type
        if (!this.allowedTypes.includes(file.type)) {
            return {
                valid: false,
                error: `Unsupported file type (${file.type})`
            };
        }

        return { valid: true };
    }
    
    removeFile(fileId) {
        this.uploadedFiles = this.uploadedFiles.filter(f => f.id != fileId);
        this.updateFileList();
        this.updateProcessButton();
    }
    
    updateFileList() {
        const fileList = document.getElementById('file-list');
        if (!fileList) return;
        
        if (this.uploadedFiles.length === 0) {
            fileList.innerHTML = '<p class="text-muted text-center">No files selected</p>';
            return;
        }
        
        fileList.innerHTML = this.uploadedFiles.map(file => `
            <div class="file-item" data-file-id="${file.id}">
                <div class="file-icon ${this.getFileIconClass(file.type)}">
                    <i class="fas ${this.getFileIcon(file.type)}"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${this.formatFileSize(file.size)}</div>
                    ${file.status !== 'ready' ? `<div class="mt-1"><span class="status-badge status-${file.status}">${file.status}</span></div>` : ''}
                </div>
                <div class="file-actions">
                    <button type="button" class="btn btn-sm btn-outline-danger remove-file" data-file-index="${file.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }
    
    updateProcessButton() {
        const processBtn = document.getElementById('process-expedition');
        if (!processBtn) return;
        
        const hasFiles = this.uploadedFiles.length > 0;
        processBtn.disabled = !hasFiles;
        
        if (hasFiles) {
            processBtn.innerHTML = `<i class="fas fa-cogs"></i> Process ${this.uploadedFiles.length} Document${this.uploadedFiles.length > 1 ? 's' : ''}`;
        } else {
            processBtn.innerHTML = '<i class="fas fa-cogs"></i> Process Documents';
        }
    }
    
    async processExpedition(e) {
        e.preventDefault();
        
        if (this.uploadedFiles.length === 0) {
            this.showError('Please select at least one document');
            return;
        }
        
        const expeditionName = document.getElementById('expedition-name')?.value || 'Unnamed Expedition';
        
        // Update UI to processing state
        this.setProcessingState(true);
        
        try {
            // Upload files first
            const uploadResults = await this.uploadFiles();
            
            if (uploadResults.success) {
                // Start processing
                await this.startProcessing(uploadResults.expeditionId, expeditionName);
            } else {
                throw new Error(uploadResults.error || 'Upload failed');
            }
            
        } catch (error) {
            this.showError(`Processing failed: ${error.message}`);
            this.setProcessingState(false);
        }
    }
    
    async uploadFiles() {
        const formData = new FormData();
        
        // Add expedition name
        const expeditionName = document.getElementById('expedition-name')?.value || 'Unnamed Expedition';
        formData.append('expedition_name', expeditionName);
        
        // Add files
        this.uploadedFiles.forEach(fileObj => {
            formData.append('files', fileObj.file);
        });
        
        try {
            const response = await fetch('/api/upload-documents', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (response.ok) {
                return { success: true, expeditionId: result.session_id };
            } else {
                return { success: false, error: result.error };
            }
            
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    async startProcessing(expeditionId, expeditionName) {
        try {
            const response = await fetch(`/api/process-expedition`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: expeditionId,
                    expedition_name: expeditionName
                })
            });

            if (response.ok) {
                // Stay on the same page and show success message
                this.showSuccess('Processing started successfully! Check the results section below.');
                this.setProcessingState(false);

                // Optionally refresh the JSON files list
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                const result = await response.json();
                throw new Error(result.error || 'Processing failed');
            }

        } catch (error) {
            throw error;
        }
    }
    
    setProcessingState(processing) {
        const processBtn = document.getElementById('process-expedition');
        const uploadArea = document.querySelector('.upload-area');
        const fileInput = document.getElementById('file-input');
        
        if (processing) {
            if (processBtn) {
                processBtn.disabled = true;
                processBtn.innerHTML = '<span class="processing-spinner"></span> Processing...';
            }
            if (uploadArea) uploadArea.style.pointerEvents = 'none';
            if (fileInput) fileInput.disabled = true;
        } else {
            if (processBtn) {
                processBtn.disabled = false;
                this.updateProcessButton();
            }
            if (uploadArea) uploadArea.style.pointerEvents = 'auto';
            if (fileInput) fileInput.disabled = false;
        }
    }
    
    // Utility methods
    getFileIcon(type) {
        if (type.includes('pdf')) return 'fa-file-pdf';
        if (type.includes('word') || type.includes('document')) return 'fa-file-word';
        if (type.includes('presentation')) return 'fa-file-powerpoint';
        if (type.includes('image')) return 'fa-file-image';
        if (type.includes('text')) return 'fa-file-text';
        return 'fa-file';
    }
    
    getFileIconClass(type) {
        if (type.includes('pdf')) return 'pdf';
        if (type.includes('word') || type.includes('document') || type.includes('presentation')) return 'doc';
        if (type.includes('image')) return 'image';
        return 'default';
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Notification methods
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'danger');
    }
    
    showWarning(message) {
        this.showNotification(message, 'warning');
    }
    
    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Add to page
        const container = document.querySelector('.container');
        if (container) {
            container.insertBefore(notification, container.firstChild);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    }
    
    setupFileValidation() {
        // Additional file validation can be added here
    }
    
    setupProgressTracking() {
        // Progress tracking for long operations
        this.trackProcessingProgress();
    }
    
    trackProcessingProgress() {
        // Check if we're on a processing page
        const expeditionId = this.getExpeditionIdFromUrl();
        if (expeditionId) {
            this.pollProcessingStatus(expeditionId);
        }
    }
    
    getExpeditionIdFromUrl() {
        const path = window.location.pathname;
        const match = path.match(/\/process\/([^\/]+)/);
        return match ? match[1] : null;
    }
    
    async pollProcessingStatus(expeditionId) {
        try {
            const response = await fetch(`/api/status/${expeditionId}`);
            const status = await response.json();
            
            this.updateProcessingUI(status);
            
            // Continue polling if still processing
            if (status.status === 'processing') {
                setTimeout(() => this.pollProcessingStatus(expeditionId), 2000);
            }
            
        } catch (error) {
            console.error('Error polling status:', error);
        }
    }
    
    updateProcessingUI(status) {
        const statusElement = document.getElementById('processing-status');
        const progressElement = document.getElementById('processing-progress');
        
        if (statusElement) {
            statusElement.textContent = status.message || status.status;
        }
        
        if (progressElement && status.progress !== undefined) {
            progressElement.style.width = `${status.progress}%`;
        }
        
        // Redirect when complete
        if (status.status === 'completed' && status.redirect_url) {
            setTimeout(() => {
                window.location.href = status.redirect_url;
            }, 1000);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ExpeditionReportConverter();
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExpeditionReportConverter;
}
