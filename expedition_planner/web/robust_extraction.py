"""
Robust extraction module for expedition data.
Provides improved extraction with validation and confidence scoring.
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)

# Try to import robust extraction components
try:
    from ..core.extraction_validator import ExtractionValidator, RobustDateParser

    HAS_VALIDATOR = True
except ImportError:
    ExtractionValidator = None
    RobustDateParser = None
    HAS_VALIDATOR = False


def extract_structured_data_with_validation(
    text_content: str, filename: str
) -> Dict[str, Any]:
    """
    Extract structured data from text content using robust parsing and validation.

    Args:
        text_content: Raw text content to extract data from
        filename: Name of the source file

    Returns:
        Dictionary of structured data with validation metadata
    """
    # Initialize validator components if available
    validator = None
    date_parser = None

    if HAS_VALIDATOR:
        try:
            validator = ExtractionValidator()
            date_parser = RobustDateParser()
            logger.info("Using robust extraction validator")
        except Exception as e:
            logger.error(f"Failed to initialize validator: {e}")

    # Initialize structure with empty/null values instead of defaults
    data = {
        "date": None,  # Don't default to current date
        "location": None,
        "arrival_time": None,
        "departure_time": None,
        "operation_type": "combined",
        "groups": [],
        "schedule": [],
        "tides": [],
        "equipment": {"zodiacs": None, "twins": None, "other": []},
        "personnel": {"total_count": 0, "guides": [], "drivers": []},
        "weather": None,
        "notes": f"Extracted from: {filename}",
    }

    # Extract date using robust date parser if available
    if HAS_VALIDATOR and date_parser:
        robust_date = date_parser.extract_date_robust(text_content)
        if robust_date:
            data["date"] = robust_date
            logger.info(f"Extracted date using robust parser: {robust_date}")
    else:
        # Fallback to basic date extraction
        extract_date_basic(text_content, data)

    # Extract location with improved patterns
    extract_location(text_content, data)

    # Extract times
    extract_times(text_content, data)

    # Extract groups
    extract_groups(text_content, data)

    # Extract schedule
    extract_schedule(text_content, data)

    # Extract equipment
    extract_equipment(text_content, data)

    # Extract personnel
    extract_personnel(text_content, data)

    # Extract weather
    extract_weather(text_content, data)

    # Validate and enhance the extracted data if validator is available
    if HAS_VALIDATOR and validator:
        try:
            enhanced_data = validator.validate_and_enhance(data, text_content)

            # Log validation results
            confidence = enhanced_data.get("_validation", {}).get("confidence", 0)
            accepted = enhanced_data.get("_validation", {}).get("accepted", False)
            warnings = enhanced_data.get("_validation", {}).get("warnings", [])

            logger.info(
                f"Extraction validation: confidence={confidence:.2f}, accepted={accepted}"
            )
            if warnings:
                logger.warning(f"Extraction warnings: {', '.join(warnings)}")

            return enhanced_data
        except Exception as e:
            logger.error(f"Validation failed: {e}")

    # Return basic data if validation is not available
    return data


def extract_date_basic(text_content: str, data: Dict[str, Any]) -> None:
    """Extract date using basic pattern matching."""
    date_patterns = [
        r"(\d{1,2}[/-]\d{1,2}[/-]\d{4})",
        r"(\d{4}[/-]\d{1,2}[/-]\d{1,2})",
        r"(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{1,2}),?\s+(\d{4})",
        r"(\d{1,2})\s+(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{4})",
    ]

    for pattern in date_patterns:
        match = re.search(pattern, text_content, re.IGNORECASE)
        if match:
            try:
                # Try to parse the date
                date_str = match.group(0)
                # Simple date parsing
                if "/" in date_str or "-" in date_str:
                    parts = re.split("[/-]", date_str)
                    if len(parts) == 3:
                        # Assume YYYY-MM-DD or MM/DD/YYYY format
                        if len(parts[0]) == 4:
                            data["date"] = (
                                f"{parts[0]}-{parts[1].zfill(2)}-{parts[2].zfill(2)}"
                            )
                        else:
                            data["date"] = (
                                f"{parts[2]}-{parts[0].zfill(2)}-{parts[1].zfill(2)}"
                            )
                break
            except Exception as e:
                logger.warning(f"Failed to parse date: {e}")
                continue


def extract_location(text_content: str, data: Dict[str, Any]) -> None:
    """Extract location with improved context awareness."""
    location_patterns = [
        r"Location:\s*([^\n\r]+)",
        r"Site:\s*([^\n\r]+)",
        r"Destination:\s*([^\n\r]+)",
        r"([A-Z][a-z]+\s+[A-Z][a-z]+\s+(?:River|Island|Bay|Reef|Falls|Point|Peninsula|Glacier|Mountain|Lake|Forest|National Park))",
        r"(?:at|in|near)\s+([A-Z][a-z]+\s+(?:River|Island|Bay|Reef|Falls|Point|Peninsula|Glacier|Mountain|Lake|Forest|National Park))",
        r"(?:at|in|near)\s+([A-Z][a-z]+\s+[A-Z][a-z]+)",
        r"Expedition to\s+([^\n\r,]+)",
    ]

    for pattern in location_patterns:
        match = re.search(pattern, text_content, re.IGNORECASE)
        if match:
            location = match.group(1).strip()
            # Filter out very short or likely invalid locations
            if len(location) > 3 and not re.match(r"^\d+$", location):
                data["location"] = location
                break


def extract_times(text_content: str, data: Dict[str, Any]) -> None:
    """Extract arrival and departure times."""
    # Extract arrival time
    arrival_patterns = [
        r"Arrival(?:\s+time)?:\s*(\d{1,2}:\d{2}(?:\s*[AP]M)?)",
        r"(?:Arrive|Arrival)(?:\s+at)?:\s*(\d{1,2}:\d{2}(?:\s*[AP]M)?)",
        r"(?:Start|Begin)(?:\s+at)?:\s*(\d{1,2}:\d{2}(?:\s*[AP]M)?)",
    ]

    for pattern in arrival_patterns:
        match = re.search(pattern, text_content, re.IGNORECASE)
        if match:
            time_str = match.group(1).strip()
            data["arrival_time"] = normalize_time(time_str)
            break

    # Extract departure time
    departure_patterns = [
        r"Departure(?:\s+time)?:\s*(\d{1,2}:\d{2}(?:\s*[AP]M)?)",
        r"(?:Depart|Departure)(?:\s+at)?:\s*(\d{1,2}:\d{2}(?:\s*[AP]M)?)",
        r"(?:End|Finish)(?:\s+at)?:\s*(\d{1,2}:\d{2}(?:\s*[AP]M)?)",
    ]

    for pattern in departure_patterns:
        match = re.search(pattern, text_content, re.IGNORECASE)
        if match:
            time_str = match.group(1).strip()
            data["departure_time"] = normalize_time(time_str)
            break


def normalize_time(time_str: str) -> str:
    """Normalize time string to 24-hour format (HH:MM)."""
    try:
        # Handle AM/PM format
        if "AM" in time_str.upper() or "PM" in time_str.upper():
            # Convert to 24-hour format
            time_parts = time_str.upper().replace(" ", "")
            is_pm = "PM" in time_parts
            time_parts = time_parts.replace("AM", "").replace("PM", "")

            hour, minute = map(int, time_parts.split(":"))
            if is_pm and hour < 12:
                hour += 12
            elif not is_pm and hour == 12:
                hour = 0

            return f"{hour:02d}:{minute:02d}"
        else:
            # Already in 24-hour format, just ensure proper formatting
            hour, minute = map(int, time_str.split(":"))
            return f"{hour:02d}:{minute:02d}"
    except Exception as e:
        logger.warning(f"Failed to normalize time '{time_str}': {e}")
        return time_str


def extract_groups(text_content: str, data: Dict[str, Any]) -> None:
    """Extract expedition groups."""
    # Look for group sections
    group_section_pattern = r"(?:Groups|Participants|Teams):\s*\n((?:.+\n)+)"
    group_match = re.search(group_section_pattern, text_content, re.IGNORECASE)

    if group_match:
        group_section = group_match.group(1)
        # Extract individual groups
        groups = []
        for line in group_section.split("\n"):
            line = line.strip()
            if line and not line.startswith("#") and len(line) > 3:
                groups.append(line)

        if groups:
            data["groups"] = groups
    else:
        # Try to find groups in bullet points or numbered lists
        bullet_pattern = r"(?:^|\n)(?:[•\-*]\s+|\d+\.\s+)([A-Z][^\n]+)"
        bullet_matches = re.findall(bullet_pattern, text_content)

        potential_groups = []
        for match in bullet_matches:
            # Filter out lines that are likely not groups
            if len(match) > 5 and not re.search(
                r"(?:equipment|arrival|departure|schedule|weather)",
                match,
                re.IGNORECASE,
            ):
                potential_groups.append(match.strip())

        if potential_groups:
            data["groups"] = potential_groups[:5]  # Limit to 5 most likely groups


def extract_schedule(text_content: str, data: Dict[str, Any]) -> None:
    """Extract expedition schedule."""
    # Look for schedule section
    schedule_section_pattern = r"(?:Schedule|Itinerary|Timeline):\s*\n((?:.+\n)+)"
    schedule_match = re.search(schedule_section_pattern, text_content, re.IGNORECASE)

    if schedule_match:
        schedule_section = schedule_match.group(1)
        # Extract schedule items
        schedule = []
        for line in schedule_section.split("\n"):
            line = line.strip()
            if line and not line.startswith("#") and len(line) > 5:
                # Try to extract time and activity
                time_activity_match = re.match(
                    r"(\d{1,2}:\d{2}(?:\s*[AP]M)?)\s*[-:]\s*(.+)", line
                )
                if time_activity_match:
                    time = normalize_time(time_activity_match.group(1))
                    activity = time_activity_match.group(2).strip() or "Activity"
                    schedule.append(
                        {
                            "time": time,
                            "activity": activity,
                            "description": activity,
                            "type": "activity",
                        }
                    )
                else:
                    # Just add the whole line as an activity with a default time
                    schedule.append(
                        {
                            "activity": line,
                            "description": line,
                            "time": "12:00",
                            "type": "activity",
                        }
                    )

        if schedule:
            data["schedule"] = schedule
    else:
        # Try to find schedule items with time patterns
        time_activity_pattern = (
            r"(?:^|\n)(\d{1,2}:\d{2}(?:\s*[AP]M)?)\s*[-:]\s*([^\n]+)"
        )
        time_activity_matches = re.findall(time_activity_pattern, text_content)

        schedule = []
        for time_str, activity in time_activity_matches:
            time = normalize_time(time_str)
            activity = activity.strip() or "Activity"
            if time:
                schedule.append(
                    {
                        "time": time,
                        "activity": activity,
                        "description": activity,
                        "type": "activity",
                    }
                )

        if schedule:
            data["schedule"] = schedule

    # If no schedule was found, create a default one
    if "schedule" not in data or not data["schedule"]:
        data["schedule"] = [
            {
                "time": "08:00",
                "activity": "Arrival",
                "description": "Arrival at location",
                "type": "arrival",
            },
            {
                "time": "12:00",
                "activity": "Departure",
                "description": "Departure from location",
                "type": "departure",
            },
        ]


def extract_equipment(text_content: str, data: Dict[str, Any]) -> None:
    """Extract equipment information."""
    # Extract zodiac count
    zodiac_pattern = r"(?:Zodiacs|Boats):\s*(\d+)"
    zodiac_match = re.search(zodiac_pattern, text_content, re.IGNORECASE)
    if zodiac_match:
        try:
            data["equipment"]["zodiacs"] = int(zodiac_match.group(1))
        except ValueError:
            pass

    # Extract twin count
    twin_pattern = r"(?:Twins|Twin engines):\s*(\d+)"
    twin_match = re.search(twin_pattern, text_content, re.IGNORECASE)
    if twin_match:
        try:
            data["equipment"]["twins"] = int(twin_match.group(1))
        except ValueError:
            pass

    # Extract other equipment
    equipment_section_pattern = r"(?:Equipment|Gear|Supplies):\s*\n((?:.+\n)+)"
    equipment_match = re.search(equipment_section_pattern, text_content, re.IGNORECASE)

    if equipment_match:
        equipment_section = equipment_match.group(1)
        other_equipment = []
        for line in equipment_section.split("\n"):
            line = line.strip()
            if line and not line.startswith("#") and len(line) > 3:
                # Skip lines that are already captured
                if not re.search(r"(?:zodiac|twin)", line, re.IGNORECASE):
                    other_equipment.append(line)

        if other_equipment:
            data["equipment"]["other"] = other_equipment


def extract_personnel(text_content: str, data: Dict[str, Any]) -> None:
    """Extract personnel information."""
    # Extract guides
    guide_section_pattern = r"(?:Guides|Leaders|Staff):\s*\n((?:.+\n)+)"
    guide_match = re.search(guide_section_pattern, text_content, re.IGNORECASE)

    if guide_match:
        guide_section = guide_match.group(1)
        guides = []
        for line in guide_section.split("\n"):
            line = line.strip()
            if line and not line.startswith("#") and len(line) > 3:
                guides.append(line)

        if guides:
            data["personnel"]["guides"] = guides
            data["personnel"]["total_count"] += len(guides)

    # Extract drivers
    driver_section_pattern = r"(?:Drivers|Operators):\s*\n((?:.+\n)+)"
    driver_match = re.search(driver_section_pattern, text_content, re.IGNORECASE)

    if driver_match:
        driver_section = driver_match.group(1)
        drivers = []
        for line in driver_section.split("\n"):
            line = line.strip()
            if line and not line.startswith("#") and len(line) > 3:
                drivers.append(line)

        if drivers:
            data["personnel"]["drivers"] = drivers
            data["personnel"]["total_count"] += len(drivers)

    # Extract total count directly if specified
    total_pattern = r"(?:Total personnel|Total staff|Total count):\s*(\d+)"
    total_match = re.search(total_pattern, text_content, re.IGNORECASE)
    if total_match:
        try:
            data["personnel"]["total_count"] = int(total_match.group(1))
        except ValueError:
            pass


def extract_weather(text_content: str, data: Dict[str, Any]) -> None:
    """Extract weather information."""
    weather_patterns = [
        r"Weather:\s*([^\n\r]+)",
        r"Conditions:\s*([^\n\r]+)",
        r"Forecast:\s*([^\n\r]+)",
    ]

    for pattern in weather_patterns:
        match = re.search(pattern, text_content, re.IGNORECASE)
        if match:
            weather = match.group(1).strip()
            if weather:
                data["weather"] = weather
                break
