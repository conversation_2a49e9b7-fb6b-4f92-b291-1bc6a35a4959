"""
Enhanced text extractor with robust OCR and validation.
Handles PDF, DOCX, and image files with clean raw text output.
Includes advanced OCR with preprocessing, multiple engines, and validation.
"""

import logging
import mimetypes
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from ..utils.error_handlers import file_processing_error_handler
from ..utils.performance import llm_optimizer, memory_efficient, performance_monitor

# Import robust OCR and validation components
try:
    from .robust_ocr import ExtractionError, RobustOCRExtractor

    HAS_ROBUST_OCR = True
except ImportError:
    RobustOCRExtractor = None
    ExtractionError = Exception
    HAS_ROBUST_OCR = False

try:
    from .extraction_validator import ExtractionValidator, RobustDateParser

    HAS_VALIDATOR = True
except ImportError:
    ExtractionValidator = None
    RobustDateParser = None
    HAS_VALIDATOR = False

# Optional imports for document processing
try:
    import fitz  # PyMuPDF

    HAS_PYMUPDF = True
except ImportError:
    fitz = None
    HAS_PYMUPDF = False

try:
    from docx import Document

    HAS_PYTHON_DOCX = True
except ImportError:
    Document = None
    HAS_PYTHON_DOCX = False

try:
    import pytesseract
    from PIL import Image

    HAS_OCR = True
except ImportError:
    pytesseract = None
    Image = None
    HAS_OCR = False

logger = logging.getLogger(__name__)


class TextExtractor:
    """Enhanced text extractor with robust OCR and validation for expedition documents."""

    def __init__(self):
        """Initialize the text extractor with robust OCR and validation."""
        self.supported_formats = {
            ".pdf": self._extract_from_pdf,
            ".docx": self._extract_from_docx,
            ".doc": self._extract_from_docx,
            ".txt": self._extract_from_txt,
            ".png": self._extract_from_image,
            ".jpg": self._extract_from_image,
            ".jpeg": self._extract_from_image,
            ".tiff": self._extract_from_image,
            ".bmp": self._extract_from_image,
        }

        # Configuration
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.ocr_enabled = True  # Enable OCR by default
        self.validation_enabled = (
            True  # Enable validation between OCR and text extraction
        )
        self.ocr_confidence_threshold = 0.7  # Minimum confidence to use OCR result

        # Initialize robust OCR extractor if available
        self.robust_ocr = None
        if HAS_ROBUST_OCR:
            try:
                self.robust_ocr = RobustOCRExtractor()
                logger.info("Robust OCR extractor initialized")
            except Exception as e:
                logger.error(f"Failed to initialize robust OCR extractor: {e}")

        # Initialize extraction validator if available
        self.validator = None
        if HAS_VALIDATOR:
            try:
                self.validator = ExtractionValidator()
                logger.info("Extraction validator initialized")
            except Exception as e:
                logger.error(f"Failed to initialize extraction validator: {e}")

        # Initialize date parser if available
        self.date_parser = None
        if HAS_VALIDATOR:
            try:
                self.date_parser = RobustDateParser()
                logger.info("Robust date parser initialized")
            except Exception as e:
                logger.error(f"Failed to initialize robust date parser: {e}")

        # Track extraction quality metrics
        self.extraction_metrics = {
            "total_extractions": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "ocr_used": 0,
            "robust_ocr_used": 0,
            "standard_extraction_used": 0,
            "combined_results_used": 0,
            "average_confidence": 0.0,
        }

    @performance_monitor
    @memory_efficient(max_memory_mb=800.0)
    @file_processing_error_handler(default_return=None)
    def extract_text(self, file_path: str) -> Optional[str]:
        """
        Extract text from a document file using robust OCR and validation.

        Args:
            file_path: Path to the document file

        Returns:
            Extracted text content or None if extraction fails
        """
        try:
            path = Path(file_path)
            self.extraction_metrics["total_extractions"] += 1

            # Validate file
            if not self._validate_file(path):
                self.extraction_metrics["failed_extractions"] += 1
                return None

            # Get file extension
            file_extension = path.suffix.lower()

            # Check if format is supported
            if file_extension not in self.supported_formats:
                logger.error(f"Unsupported file format: {file_extension}")
                self.extraction_metrics["failed_extractions"] += 1
                return None

            # For image files, use robust OCR if available
            if file_extension in [".png", ".jpg", ".jpeg", ".tiff", ".bmp"]:
                logger.info(f"Using OCR for image file: {file_path}")

                if self.robust_ocr:
                    try:
                        logger.info(
                            f"Using robust OCR with preprocessing for {file_path}"
                        )
                        text = self.robust_ocr.extract_with_quality_check(
                            str(path), min_confidence=self.ocr_confidence_threshold
                        )
                        self.extraction_metrics["robust_ocr_used"] += 1
                        self.extraction_metrics["successful_extractions"] += 1
                        return self._clean_text(text)
                    except ExtractionError as e:
                        logger.warning(f"Robust OCR failed with quality error: {e}")
                        # Fall back to standard OCR
                        logger.info(f"Falling back to standard OCR for {file_path}")

                # Standard OCR fallback
                text = self._extract_from_image(path)
                if text:
                    self.extraction_metrics["ocr_used"] += 1
                    self.extraction_metrics["successful_extractions"] += 1
                    return self._clean_text(text)

                self.extraction_metrics["failed_extractions"] += 1
                return None

            # For PDF files, use robust extraction with validation
            if file_extension == ".pdf" and self.ocr_enabled:
                if self.robust_ocr and self.validation_enabled:
                    logger.info(
                        f"Using robust PDF extraction with OCR validation for {file_path}"
                    )
                    return self._extract_pdf_with_robust_ocr(path)
                elif self.validation_enabled:
                    logger.info(
                        f"Using dual extraction (standard + OCR) for {file_path}"
                    )
                    return self._extract_with_validation(path)
                else:
                    # Standard PDF extraction
                    text = self._extract_from_pdf(path)
                    if text:
                        self.extraction_metrics["standard_extraction_used"] += 1
                        self.extraction_metrics["successful_extractions"] += 1
                        return self._clean_text(text)

                    self.extraction_metrics["failed_extractions"] += 1
                    return None

            # For other formats, use the standard extraction method
            extractor_method = self.supported_formats[file_extension]
            text = extractor_method(path)

            if text:
                logger.info(f"Successfully extracted text from: {file_path}")
                self.extraction_metrics["standard_extraction_used"] += 1
                self.extraction_metrics["successful_extractions"] += 1
                return self._clean_text(text)
            else:
                logger.warning(f"No text extracted from: {file_path}")
                self.extraction_metrics["failed_extractions"] += 1
                return None

        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            self.extraction_metrics["failed_extractions"] += 1
            return None

    @performance_monitor
    @memory_efficient(max_memory_mb=1000.0)
    def _extract_pdf_with_robust_ocr(self, path: Path) -> Optional[str]:
        """
        Extract text from PDF using robust OCR with preprocessing and validation.

        Args:
            path: Path to PDF file

        Returns:
            Extracted text or None if extraction fails
        """
        if not self.robust_ocr:
            logger.warning(
                "Robust OCR not available, falling back to standard extraction"
            )
            return self._extract_with_validation(path)

        try:
            # First try standard extraction
            standard_text = self._extract_from_pdf(path)

            # Initialize results
            all_page_texts = []
            ocr_used_pages = 0
            standard_used_pages = 0

            # Open PDF
            import fitz

            doc = fitz.open(str(path))

            # Process each page
            for page_num in range(len(doc)):
                try:
                    # Get standard text for this page
                    page = doc.load_page(page_num)
                    page_text = page.get_text()

                    # Check if page has sufficient text
                    if len(page_text.strip()) > 100:
                        logger.info(
                            f"Using standard extraction for page {page_num + 1} (sufficient text)"
                        )
                        all_page_texts.append(page_text)
                        standard_used_pages += 1
                        continue

                    # Page has little or no text, try robust OCR
                    logger.info(
                        f"Using robust OCR for page {page_num + 1} (insufficient standard text)"
                    )
                    try:
                        ocr_text = self.robust_ocr.extract_from_pdf_page(
                            str(path),
                            page_num,
                            min_confidence=self.ocr_confidence_threshold,
                        )
                        all_page_texts.append(ocr_text)
                        ocr_used_pages += 1
                    except ExtractionError as e:
                        logger.warning(
                            f"Robust OCR failed for page {page_num + 1}: {e}"
                        )
                        # Use whatever text we got from standard extraction
                        if page_text.strip():
                            all_page_texts.append(page_text)
                            standard_used_pages += 1

                except Exception as e:
                    logger.error(f"Error processing page {page_num + 1}: {e}")
                    # Try to get whatever text we can
                    try:
                        page = doc.load_page(page_num)
                        page_text = page.get_text()
                        if page_text.strip():
                            all_page_texts.append(page_text)
                            standard_used_pages += 1
                    except:
                        pass

            # Close the document
            doc.close()

            # Combine all page texts
            if all_page_texts:
                combined_text = "\n\n".join(all_page_texts)

                # Update metrics
                if ocr_used_pages > 0 and standard_used_pages > 0:
                    self.extraction_metrics["combined_results_used"] += 1
                    logger.info(
                        f"Used combined extraction: {standard_used_pages} standard pages, {ocr_used_pages} OCR pages"
                    )
                elif ocr_used_pages > 0:
                    self.extraction_metrics["robust_ocr_used"] += 1
                    logger.info(f"Used robust OCR for all {ocr_used_pages} pages")
                else:
                    self.extraction_metrics["standard_extraction_used"] += 1
                    logger.info(
                        f"Used standard extraction for all {standard_used_pages} pages"
                    )

                self.extraction_metrics["successful_extractions"] += 1
                return self._clean_text(combined_text)

            # If we couldn't extract any text, fall back to standard extraction
            if standard_text:
                self.extraction_metrics["standard_extraction_used"] += 1
                self.extraction_metrics["successful_extractions"] += 1
                logger.info("Falling back to standard extraction for the entire PDF")
                return self._clean_text(standard_text)

            # If all else fails, try the original OCR method
            logger.info("Trying original OCR method as last resort")
            ocr_text = self._extract_pdf_with_ocr(path)
            if ocr_text:
                self.extraction_metrics["ocr_used"] += 1
                self.extraction_metrics["successful_extractions"] += 1
                return self._clean_text(ocr_text)

            # No text could be extracted
            self.extraction_metrics["failed_extractions"] += 1
            logger.warning(f"No text could be extracted from PDF: {path}")
            return None

        except Exception as e:
            logger.error(f"Error in robust PDF extraction: {e}")
            # Fall back to standard validation
            return self._extract_with_validation(path)

    @performance_monitor
    def _extract_with_validation(self, path: Path) -> Optional[str]:
        """
        Extract text using both standard extraction and OCR, then validate and combine results.

        Args:
            path: Path to the document file

        Returns:
            Best extracted text content or None if extraction fails
        """
        try:
            # Extract using standard method
            standard_text = self._extract_from_pdf(path)

            # Extract using OCR
            ocr_text = self._extract_pdf_with_ocr(path)

            # If either method fails, return the successful one
            if not standard_text and not ocr_text:
                logger.warning(f"Both extraction methods failed for {path}")
                return None
            elif not standard_text:
                logger.info(f"Using OCR result for {path} (standard extraction failed)")
                return self._clean_text(ocr_text)
            elif not ocr_text:
                logger.info(f"Using standard extraction result for {path} (OCR failed)")
                return self._clean_text(standard_text)

            # Compare and validate results
            result, confidence = self._validate_extraction_results(
                standard_text, ocr_text
            )

            logger.info(
                f"Extraction validation complete for {path} with confidence: {confidence:.2f}"
            )
            return self._clean_text(result)

        except Exception as e:
            logger.error(f"Error in dual extraction for {path}: {e}")
            return None

    def _validate_extraction_results(
        self, standard_text: str, ocr_text: str
    ) -> Tuple[str, float]:
        """
        Compare standard extraction and OCR results to determine the best text.

        Args:
            standard_text: Text from standard extraction
            ocr_text: Text from OCR

        Returns:
            Tuple of (best_text, confidence_score)
        """
        # Clean both texts for comparison
        clean_standard = self._clean_text(standard_text)
        clean_ocr = self._clean_text(ocr_text)

        # Simple metrics for comparison
        standard_len = len(clean_standard)
        ocr_len = len(clean_ocr)

        # Calculate similarity (simple approach)
        # For a more sophisticated approach, consider using difflib or Levenshtein distance
        common_words_standard = set(re.findall(r"\b\w+\b", clean_standard.lower()))
        common_words_ocr = set(re.findall(r"\b\w+\b", clean_ocr.lower()))

        if not common_words_standard or not common_words_ocr:
            # If either set is empty, use the non-empty one
            if common_words_standard:
                return clean_standard, 1.0
            else:
                return clean_ocr, 1.0

        # Calculate Jaccard similarity
        intersection = len(common_words_standard.intersection(common_words_ocr))
        union = len(common_words_standard.union(common_words_ocr))
        similarity = intersection / union if union > 0 else 0

        # Check for key indicators of quality
        standard_quality = self._assess_text_quality(clean_standard)
        ocr_quality = self._assess_text_quality(clean_ocr)

        # Decision logic
        if similarity > 0.8:
            # Results are very similar, prefer the longer one
            if standard_len > ocr_len * 1.2:
                logger.info("Using standard extraction (longer with high similarity)")
                return clean_standard, 0.9
            elif ocr_len > standard_len * 1.2:
                logger.info("Using OCR result (longer with high similarity)")
                return clean_ocr, 0.9
            else:
                # Similar length, use standard extraction
                logger.info("Using standard extraction (similar length and content)")
                return clean_standard, 0.8
        else:
            # Results differ significantly
            if standard_quality > ocr_quality * 1.5:
                logger.info("Using standard extraction (higher quality score)")
                return clean_standard, standard_quality / 10
            elif ocr_quality > standard_quality * 1.5:
                logger.info("Using OCR result (higher quality score)")
                return clean_ocr, ocr_quality / 10
            else:
                # Quality scores are similar, combine results
                logger.info(
                    "Combining results (different content with similar quality)"
                )
                combined = self._combine_extraction_results(clean_standard, clean_ocr)
                return combined, 0.7

    def _assess_text_quality(self, text: str) -> float:
        """
        Assess the quality of extracted text using multiple metrics.

        Args:
            text: Extracted text

        Returns:
            Quality score (0-10)
        """
        if not text:
            return 0

        # Count words and sentences
        words = re.findall(r"\b\w+\b", text)
        sentences = re.split(r"[.!?]+", text)

        # Remove empty sentences
        sentences = [s for s in sentences if s.strip()]

        if not words:
            return 0

        # Calculate basic metrics
        avg_word_length = sum(len(word) for word in words) / len(words) if words else 0
        words_per_sentence = len(words) / len(sentences) if sentences else 0
        total_words = len(words)
        total_sentences = len(sentences)

        # 1. Word length score (0-10)
        # Optimal average word length is around 5 characters
        word_length_score = 10 - min(10, abs(avg_word_length - 5) * 2)

        # 2. Sentence structure score (0-10)
        # Optimal sentence length is around 10-15 words
        sentence_score = 10 - min(10, abs(words_per_sentence - 12) * 0.5)

        # 3. Content density score (0-10)
        # Measures the ratio of unique words to total words
        unique_words = len(set(word.lower() for word in words))
        density_ratio = unique_words / total_words if total_words > 0 else 0
        # Optimal density is around 0.6-0.7 (some repetition is normal)
        density_score = 10 * min(1.0, density_ratio * 1.5)

        # 4. Garbage text detection (0-10)
        garbage_patterns = [
            r"�",  # Unicode replacement character
            r"###",  # Common OCR placeholder
            r"\[\?\]",  # Question mark in brackets
            r"\[\.\.\.\]",  # Ellipsis in brackets
            r"\<\/?[a-z]+\>",  # HTML-like tags
            r"[^\w\s.,;:!?()-]+",  # Clusters of unusual characters
        ]

        garbage_score = 10
        for pattern in garbage_patterns:
            matches = len(re.findall(pattern, text))
            if matches > 0:
                garbage_score -= min(10, matches * 0.5)

        garbage_score = max(0, garbage_score)

        # 5. Structural coherence score (0-10)
        # Check for presence of expected document structures
        structure_score = 0

        # Check for date patterns
        if re.search(r"\d{1,2}[/-]\d{1,2}[/-]\d{4}|\d{4}[/-]\d{1,2}[/-]\d{1,2}", text):
            structure_score += 2

        # Check for time patterns
        if re.search(r"\d{1,2}:\d{2}(?:\s*[AP]M)?", text):
            structure_score += 2

        # Check for location patterns
        if re.search(
            r"(?:Location|Site|Destination):\s*([^\n\r]+)", text, re.IGNORECASE
        ):
            structure_score += 2

        # Check for section headers
        if re.search(
            r"(?:Schedule|Itinerary|Groups|Equipment|Personnel):", text, re.IGNORECASE
        ):
            structure_score += 2

        # Check for reasonable paragraph structure
        paragraphs = [p for p in text.split("\n\n") if p.strip()]
        if len(paragraphs) >= 3:
            structure_score += 2

        # Calculate weighted final score (0-10)
        # Weight factors based on importance
        weights = {
            "word_length": 0.15,
            "sentence": 0.15,
            "density": 0.2,
            "garbage": 0.3,
            "structure": 0.2,
        }

        quality_score = (
            word_length_score * weights["word_length"]
            + sentence_score * weights["sentence"]
            + density_score * weights["density"]
            + garbage_score * weights["garbage"]
            + structure_score * weights["structure"]
        )

        # Log detailed quality assessment
        logger.debug(
            f"Text quality assessment: word_length={word_length_score:.1f}, "
            f"sentence={sentence_score:.1f}, density={density_score:.1f}, "
            f"garbage={garbage_score:.1f}, structure={structure_score:.1f}, "
            f"final={quality_score:.1f}"
        )

        return min(10, max(0, quality_score))

    def _combine_extraction_results(self, text1: str, text2: str) -> str:
        """
        Combine two extraction results intelligently with section-aware merging.

        Args:
            text1: First extracted text (typically standard extraction)
            text2: Second extracted text (typically OCR extraction)

        Returns:
            Combined text with the best elements from both sources
        """
        # If one text is empty, return the other
        if not text1.strip():
            return text2
        if not text2.strip():
            return text1

        # Identify which text is likely from standard extraction vs OCR
        # Standard extraction typically has better structure but might miss content
        # OCR typically has more content but might have more errors

        # Heuristic: Standard extraction usually has better paragraph structure
        paragraphs1 = [p.strip() for p in text1.split("\n\n") if p.strip()]
        paragraphs2 = [p.strip() for p in text2.split("\n\n") if p.strip()]

        # If one text has significantly more paragraphs, it's likely the better structured one
        if len(paragraphs1) > len(paragraphs2) * 1.5:
            structured_text, ocr_text = text1, text2
            structured_paragraphs, ocr_paragraphs = paragraphs1, paragraphs2
        elif len(paragraphs2) > len(paragraphs1) * 1.5:
            structured_text, ocr_text = text2, text1
            structured_paragraphs, ocr_paragraphs = paragraphs2, paragraphs1
        else:
            # If paragraph counts are similar, use the one with more section headers
            headers1 = len(
                re.findall(r"(?:^|\n)([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*:)", text1)
            )
            headers2 = len(
                re.findall(r"(?:^|\n)([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*:)", text2)
            )

            if headers1 >= headers2:
                structured_text, ocr_text = text1, text2
                structured_paragraphs, ocr_paragraphs = paragraphs1, paragraphs2
            else:
                structured_text, ocr_text = text2, text1
                structured_paragraphs, ocr_paragraphs = paragraphs2, paragraphs1

        # Identify key sections in both texts
        section_patterns = [
            (r"(?:^|\n)(?:Date|Expedition Date):\s*([^\n]+)", "date"),
            (r"(?:^|\n)(?:Location|Site|Destination):\s*([^\n]+)", "location"),
            (
                r"(?:^|\n)(?:Schedule|Itinerary|Timeline)(?::|[\s\n])((?:.+\n)+?(?:\n|$))",
                "schedule",
            ),
            (
                r"(?:^|\n)(?:Groups|Teams|Participants)(?::|[\s\n])((?:.+\n)+?(?:\n|$))",
                "groups",
            ),
            (
                r"(?:^|\n)(?:Equipment|Gear|Supplies)(?::|[\s\n])((?:.+\n)+?(?:\n|$))",
                "equipment",
            ),
            (
                r"(?:^|\n)(?:Personnel|Staff|Guides)(?::|[\s\n])((?:.+\n)+?(?:\n|$))",
                "personnel",
            ),
            (r"(?:^|\n)(?:Weather|Conditions):\s*([^\n]+)", "weather"),
            (
                r"(?:^|\n)(?:Notes|Comments|Additional Information)(?::|[\s\n])((?:.+\n)+?(?:\n|$))",
                "notes",
            ),
        ]

        # Extract sections from both texts
        structured_sections = {}
        ocr_sections = {}

        for pattern, section_name in section_patterns:
            # Extract from structured text
            structured_match = re.search(pattern, structured_text, re.IGNORECASE)
            if structured_match:
                structured_sections[section_name] = structured_match.group(1).strip()

            # Extract from OCR text
            ocr_match = re.search(pattern, ocr_text, re.IGNORECASE)
            if ocr_match:
                ocr_sections[section_name] = ocr_match.group(1).strip()

        # Start with the structured text as the base
        combined_text = structured_text

        # For each section found in OCR but not in structured text, add it
        for section_name, content in ocr_sections.items():
            if section_name not in structured_sections:
                # Format the section header properly
                header = section_name.title() + ":"
                combined_text += f"\n\n{header}\n{content}"

        # Find unique paragraphs in OCR text that aren't in structured text
        unique_paragraphs = []
        for para in ocr_paragraphs:
            # Skip very short paragraphs
            if len(para) < 20:
                continue

            # Check if this paragraph is significantly different from all structured paragraphs
            is_unique = True
            for base_para in structured_paragraphs:
                # Extract words for comparison
                words_base = set(re.findall(r"\b\w+\b", base_para.lower()))
                words_ocr = set(re.findall(r"\b\w+\b", para.lower()))

                if not words_ocr:
                    is_unique = False
                    break

                if words_base and words_ocr:
                    # Calculate Jaccard similarity
                    intersection = len(words_base.intersection(words_ocr))
                    union = len(words_base.union(words_ocr))
                    similarity = intersection / union if union > 0 else 0

                    # If paragraphs are similar, don't add
                    if similarity > 0.4:  # Lower threshold to be more selective
                        is_unique = False
                        break

            if is_unique:
                # Check if paragraph has meaningful content
                if len(re.findall(r"\b\w+\b", para)) > 5:  # At least 5 words
                    unique_paragraphs.append(para)

        # Add unique paragraphs at the end under "Additional Information" if not already present
        if unique_paragraphs and not re.search(
            r"(?:^|\n)Additional Information:", combined_text, re.IGNORECASE
        ):
            additional_content = "\n\nAdditional Information:\n" + "\n\n".join(
                unique_paragraphs
            )
            combined_text += additional_content

        # Log combination results
        logger.info(
            f"Combined extraction results: {len(structured_text)} chars from structured text, "
            f"{len(ocr_text)} chars from OCR text, resulting in {len(combined_text)} chars"
        )

        return combined_text

    def extract_from_multiple_files(self, file_paths: List[str]) -> List[str]:
        """
        Extract text from multiple files.

        Args:
            file_paths: List of file paths to process

        Returns:
            List of extracted text content (in same order as input files)
        """
        results = []

        for file_path in file_paths:
            text = self.extract_text(file_path)
            if text:
                results.append(text)
            else:
                logger.warning(f"Failed to extract text from: {file_path}")
                results.append("")  # Add empty string to maintain order

        return results

    def _validate_file(self, path: Path) -> bool:
        """Validate if a file can be processed."""
        try:
            # Check if file exists
            if not path.exists():
                logger.error(f"File not found: {path}")
                return False

            # Check file size
            file_size = path.stat().st_size
            if file_size > self.max_file_size:
                logger.error(
                    f"File too large: {file_size} bytes (max: {self.max_file_size})"
                )
                return False

            # Check if file is readable
            if not path.is_file():
                logger.error(f"Path is not a file: {path}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating file {path}: {e}")
            return False

    def _extract_from_pdf(self, path: Path) -> Optional[str]:
        """Extract text from PDF using PyMuPDF."""
        if not HAS_PYMUPDF:
            logger.error(
                "PyMuPDF (fitz) not installed. Install with: pip install PyMuPDF"
            )
            return None

        try:
            doc = fitz.open(str(path))
            text_content = []

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                if text.strip():
                    text_content.append(text)

            doc.close()

            if text_content:
                return "\n\n".join(text_content)
            else:
                logger.warning(f"No text found in PDF: {path}")
                return None

        except Exception as e:
            logger.error(f"Error extracting from PDF {path}: {e}")
            return None

    def _extract_pdf_with_ocr(self, path: Path) -> Optional[str]:
        """Extract text from PDF using OCR."""
        if not HAS_OCR or not HAS_PYMUPDF:
            logger.error("OCR or PyMuPDF not available for PDF OCR extraction")
            return None

        try:
            # Open the PDF
            doc = fitz.open(str(path))
            text_content = []

            # Process each page
            for page_num in range(len(doc)):
                try:
                    page = doc.load_page(page_num)

                    # Get the page as an image
                    pix = page.get_pixmap(
                        matrix=fitz.Matrix(2, 2)
                    )  # 2x zoom for better OCR

                    # Convert to PIL Image
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                    # Perform OCR
                    text = pytesseract.image_to_string(img)

                    if text.strip():
                        text_content.append(text)

                except Exception as page_error:
                    logger.warning(
                        f"Error OCR processing page {page_num} of {path}: {page_error}"
                    )
                    continue

            doc.close()

            if text_content:
                return "\n\n".join(text_content)
            else:
                logger.warning(f"No text found via OCR in PDF: {path}")
                return None

        except Exception as e:
            logger.error(f"Error extracting from PDF with OCR {path}: {e}")
            return None

    def _extract_from_docx(self, path: Path) -> Optional[str]:
        """Extract text from DOCX using python-docx."""
        if not HAS_PYTHON_DOCX:
            logger.error(
                "python-docx not installed. Install with: pip install python-docx"
            )
            return None

        try:
            doc = Document(str(path))
            text_content = []

            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))

            if text_content:
                return "\n".join(text_content)
            else:
                logger.warning(f"No text found in DOCX: {path}")
                return None

        except Exception as e:
            logger.error(f"Error extracting from DOCX {path}: {e}")
            return None

    def _extract_from_txt(self, path: Path) -> Optional[str]:
        """Extract text from plain text file."""
        try:
            with open(path, encoding="utf-8") as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(path, encoding="latin-1") as f:
                    return f.read()
            except Exception as e:
                logger.error(f"Error reading text file {path}: {e}")
                return None
        except Exception as e:
            logger.error(f"Error extracting from text file {path}: {e}")
            return None

    def _extract_from_image(self, path: Path) -> Optional[str]:
        """
        Extract text from image using OCR with preprocessing.
        This is the standard OCR method, used as fallback when robust OCR is not available.
        """
        if not HAS_OCR:
            logger.error(
                "pytesseract or PIL not installed. Install with: pip install pytesseract pillow"
            )
            logger.error("Also ensure tesseract is installed on your system")
            return None

        try:
            # Open image
            image = Image.open(str(path))

            # Basic preprocessing for better OCR results
            try:
                # Convert to grayscale
                image = image.convert("L")

                # Enhance contrast
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(2.0)

                # Apply some sharpening
                image = image.filter(ImageFilter.SHARPEN)
            except Exception as preprocess_error:
                logger.warning(f"Image preprocessing failed: {preprocess_error}")
                # Reopen the original image
                image = Image.open(str(path))

            # Extract text using OCR with improved configuration
            try:
                # Try with improved configuration
                text = pytesseract.image_to_string(
                    image,
                    config="--psm 6 --oem 3",  # Page segmentation mode 6 (assume single block of text)
                )
            except Exception as config_error:
                logger.warning(f"OCR with custom config failed: {config_error}")
                # Fall back to default configuration
                text = pytesseract.image_to_string(image)

            if text.strip():
                # Try to get confidence data
                try:
                    data = pytesseract.image_to_data(
                        image, output_type=pytesseract.Output.DICT
                    )
                    confidences = [float(conf) for conf in data["conf"] if conf != "-1"]
                    avg_confidence = (
                        sum(confidences) / len(confidences) if confidences else 0
                    )
                    logger.info(f"OCR confidence for {path}: {avg_confidence:.2f}%")

                    # If confidence is too low, log a warning
                    if avg_confidence < self.ocr_confidence_threshold * 100:
                        logger.warning(
                            f"Low OCR confidence ({avg_confidence:.2f}%) for {path}"
                        )
                except Exception as conf_error:
                    logger.warning(f"Could not get OCR confidence: {conf_error}")

                return text
            else:
                logger.warning(f"No text found in image: {path}")
                return None

        except Exception as e:
            logger.error(f"Error extracting from image {path}: {e}")
            return None

    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text."""
        if not text:
            return ""

        import re

        # Remove hyphenated line breaks (e.g., "King-\nGeorge" -> "KingGeorge")
        text = re.sub(r"(\w+)-\n(\w+)", r"\1\2", text)

        # Normalize line breaks and strip whitespace
        lines = text.splitlines()
        cleaned_lines = [line.strip() for line in lines if line.strip()]

        # Collapse multiple spaces to single
        cleaned_text = "\n".join(cleaned_lines)
        cleaned_text = re.sub(r" +", " ", cleaned_text)

        return cleaned_text


def create_text_extractor(config: Optional[Dict[str, Any]] = None) -> TextExtractor:
    """
    Create a text extractor instance with robust OCR capabilities.

    Args:
        config: Optional configuration dictionary with settings

    Returns:
        Configured TextExtractor instance
    """
    extractor = TextExtractor()

    # Apply configuration if provided
    if config:
        if "ocr_enabled" in config:
            extractor.ocr_enabled = config["ocr_enabled"]
        if "validation_enabled" in config:
            extractor.validation_enabled = config["validation_enabled"]
        if "ocr_confidence_threshold" in config:
            extractor.ocr_confidence_threshold = config["ocr_confidence_threshold"]

    # Log initialization status
    ocr_status = []
    if HAS_OCR:
        ocr_status.append("Standard OCR (Tesseract)")
    if HAS_ROBUST_OCR:
        ocr_status.append("Robust OCR")
    if HAS_VALIDATOR:
        ocr_status.append("Extraction Validator")

    if ocr_status:
        logger.info(f"Text extractor initialized with: {', '.join(ocr_status)}")
    else:
        logger.warning("Text extractor initialized without OCR capabilities")

    return extractor
