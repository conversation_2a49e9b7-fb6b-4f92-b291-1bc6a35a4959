"""
Enhanced template processor using agent-based architecture.
Coordinates document processing, extraction, and pattern recognition using LangChain agents.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..agents.data_extractor import DataExtractionAgent
from ..agents.json_generator import JSONGeneratorAgent
from ..agents.pattern_analyzer import PatternAnalysisAgent
from ..data.template_models import ExpeditionTemplate, OperationalPattern

logger = logging.getLogger(__name__)


class TemplateProcessor:
    """Enhanced processor for generating expedition JSON templates using agents."""

    def __init__(self):
        self.data_extractor = DataExtractionAgent()
        self.pattern_analyzer = PatternAnalysisAgent()
        self.json_generator = JSONGeneratorAgent()
        self.processed_templates = []
        self.identified_patterns = []

    def process_expedition_documents(
        self, documents: List[str], location: str
    ) -> Dict[str, Any]:
        """
        Process expedition documents to generate a JSON template for a location.

        Args:
            documents: List of document file paths
            location: Location name for the expedition

        Returns:
            Dictionary containing the template and processing results
        """
        try:
            logger.info(
                f"Processing {len(documents)} documents for location: {location}"
            )

            # Step 1: Extract structured data using the enhanced agent
            extraction_result = self.data_extractor.extract_from_documents(
                documents, location
            )

            if not extraction_result["success"]:
                return {
                    "error": f"Data extraction failed: {extraction_result.get('error', 'Unknown error')}",
                    "template": {},
                    "metadata": {
                        "location": location,
                        "processing_timestamp": datetime.now().isoformat(),
                        "source_documents": documents,
                        "completeness_score": 0.0,
                        "extraction_method": "failed",
                    },
                }

            extracted_data = extraction_result["extracted_data"]
            logger.info(
                f"Successfully extracted data from {len(extraction_result['processed_files'])} files"
            )

            # Step 2: Analyze patterns in the extracted data
            try:
                pattern_analysis = self.pattern_analyzer.analyze_expedition_data(
                    extracted_data, location
                )
                logger.info("Completed pattern analysis")
            except Exception as e:
                logger.warning(f"Pattern analysis failed: {e}")
                pattern_analysis = {"success": False, "error": str(e)}

            # Step 3: Create expedition template from extracted data
            template = self._create_template_from_data(
                extracted_data, location, documents
            )

            # Step 4: Apply pattern insights to template
            if pattern_analysis.get("success", False):
                template = self._apply_pattern_insights(
                    template, pattern_analysis.get("analysis", {})
                )

            # Step 5: Calculate completeness score
            template.completenessScore = self._calculate_completeness_score_from_data(
                extracted_data
            )

            # Store processed template
            self.processed_templates.append(template)

            # Generate results
            results = {
                "template": template.to_dict(),
                "metadata": {
                    "location": location,
                    "processing_timestamp": datetime.now().isoformat(),
                    "source_documents": documents,
                    "completeness_score": template.completenessScore,
                    "extraction_method": "agent_based",
                    "processed_files": extraction_result.get("processed_files", []),
                },
                "extracted_data": extracted_data,
                "pattern_analysis": pattern_analysis,
                "template_validation": self._validate_template(template),
            }

            logger.info(
                f"Template generated with {template.completenessScore:.1%} completeness using agent-based extraction"
            )
            return results

        except Exception as e:
            logger.error(f"Error processing expedition documents: {e}")
            return {
                "error": str(e),
                "template": {},
                "metadata": {
                    "location": location,
                    "processing_timestamp": datetime.now().isoformat(),
                    "source_documents": documents,
                    "completeness_score": 0.0,
                    "extraction_method": "failed",
                },
            }

    def process_multiple_locations(
        self, location_documents: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """
        Process documents for multiple locations and identify cross-location patterns.

        Args:
            location_documents: Dict mapping location names to document lists

        Returns:
            Dictionary containing all templates and global patterns
        """
        results = {
            "templates": {},
            "global_patterns": [],
            "processing_summary": {
                "total_locations": len(location_documents),
                "processing_timestamp": datetime.now().isoformat(),
                "completeness_scores": {},
            },
        }

        # Process each location
        for location, documents in location_documents.items():
            location_result = self.process_expedition_documents(documents, location)
            results["templates"][location] = location_result
            results["processing_summary"]["completeness_scores"][location] = (
                location_result.get("metadata", {}).get("completeness_score", 0.0)
            )

        # Analyze global patterns across all locations
        if len(self.processed_templates) >= 2:
            global_patterns = self.pattern_recognizer.analyze_templates(
                self.processed_templates
            )
            results["global_patterns"] = [p.to_dict() for p in global_patterns]
            self.identified_patterns = global_patterns

        return results

    def _update_patterns(self):
        """Update pattern recognition with latest templates."""
        if len(self.processed_templates) >= 3:  # Need minimum data for pattern analysis
            new_patterns = self.pattern_recognizer.analyze_templates(
                self.processed_templates
            )
            self.identified_patterns = new_patterns

    def _generate_pattern_insights(
        self, template: ExpeditionTemplate
    ) -> List[Dict[str, Any]]:
        """Generate human-readable insights from identified patterns."""
        insights = []

        for pattern in template.identifiedPatterns:
            insight = {
                "type": pattern.pattern_type,
                "description": self._pattern_to_description(pattern),
                "confidence": pattern.confidence,
                "examples": pattern.examples[:3],  # Limit examples
            }
            insights.append(insight)

        # Add template-specific insights
        if template.schedule:
            arrival_events = [e for e in template.schedule if e.type == "arrival"]
            if arrival_events:
                arrival_time = arrival_events[0].time
                if arrival_time:
                    hour = int(arrival_time.split(":")[0])
                    if hour >= 12:
                        insights.append(
                            {
                                "type": "timing_analysis",
                                "description": f"Afternoon arrival ({arrival_time}) suggests PM-only operation",
                                "confidence": 0.8,
                                "examples": [f"Arrival at {arrival_time}"],
                            }
                        )
                    elif hour <= 7:
                        insights.append(
                            {
                                "type": "timing_analysis",
                                "description": f"Early arrival ({arrival_time}) suggests full-day operation",
                                "confidence": 0.8,
                                "examples": [f"Arrival at {arrival_time}"],
                            }
                        )

        return insights

    def _pattern_to_description(self, pattern: OperationalPattern) -> str:
        """Convert pattern to human-readable description."""
        descriptions = {
            "eta_operation_type": {
                "eta_after_10am": "Late arrivals typically indicate PM-only operations",
                "eta_before_8am": "Early arrivals typically indicate full-day operations",
            },
            "group_scheduling": {
                "multiple_groups": "Groups are typically scheduled with consistent intervals"
            },
            "equipment_activity": {
                "activity_guided_landing": "Guided landings have consistent equipment requirements"
            },
            "tide_preference": {
                "landing_operations": "Landing operations show preference for specific tide conditions"
            },
        }

        pattern_desc = descriptions.get(pattern.pattern_type, {})
        return pattern_desc.get(
            pattern.condition, f"Pattern identified: {pattern.result}"
        )

    def _validate_template(self, template: ExpeditionTemplate) -> Dict[str, Any]:
        """Validate template completeness and consistency."""
        validation = {
            "is_valid": True,
            "warnings": [],
            "missing_fields": [],
            "consistency_checks": [],
        }

        # Check required fields
        required_fields = [
            ("location", template.location),
            ("date", template.date),
            ("groups", template.groups),
            ("schedule", template.schedule),
        ]

        for field_name, field_value in required_fields:
            if not field_value:
                validation["missing_fields"].append(field_name)
                validation["is_valid"] = False

        # Consistency checks
        if template.groups and template.schedule:
            # Check if group times align with schedule
            group_times = [g.departureTime for g in template.groups if g.departureTime]
            schedule_times = [e.time for e in template.schedule if e.time]

            if group_times and not any(gt in schedule_times for gt in group_times):
                validation["warnings"].append(
                    "Group departure times not found in schedule"
                )

        if template.zodiacs and template.groups:
            # Check if zodiac count makes sense for group count
            if template.zodiacs < len(template.groups):
                validation["warnings"].append(
                    f"Only {template.zodiacs} zodiacs for {len(template.groups)} groups"
                )

        # Check time format consistency
        time_fields = []
        if template.schedule:
            time_fields.extend([e.time for e in template.schedule if e.time])
        if template.groups:
            time_fields.extend(
                [g.departureTime for g in template.groups if g.departureTime]
            )
            time_fields.extend([g.returnTime for g in template.groups if g.returnTime])

        invalid_times = [t for t in time_fields if not self._is_valid_time_format(t)]
        if invalid_times:
            validation["warnings"].append(f"Invalid time formats: {invalid_times}")

        return validation

    def _is_valid_time_format(self, time_str: str) -> bool:
        """Check if time string is in valid HH:MM format."""
        if not time_str:
            return False

        if not isinstance(time_str, str):
            return False

        try:
            parts = time_str.split(":")
            if len(parts) != 2:
                return False

            hour, minute = int(parts[0]), int(parts[1])
            return 0 <= hour <= 23 and 0 <= minute <= 59
        except (ValueError, IndexError, AttributeError):
            return False

    def export_template_json(
        self, template_result: Dict[str, Any], output_path: str
    ) -> str:
        """Export template to JSON file."""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            # Create clean JSON output
            json_output = {
                **template_result["template"],
                "_metadata": {
                    "generated_at": template_result["metadata"]["processing_timestamp"],
                    "completeness_score": template_result["metadata"][
                        "completeness_score"
                    ],
                    "source_documents": template_result["metadata"]["source_documents"],
                },
                "_pattern_insights": template_result.get("pattern_insights", []),
                "_validation": template_result.get("template_validation", {}),
            }

            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(json_output, f, indent=2, ensure_ascii=False)

            logger.info(f"Template exported to {output_file}")
            return str(output_file)

        except Exception as e:
            logger.error(f"Error exporting template: {e}")
            raise

    def get_pattern_summary(self) -> Dict[str, Any]:
        """Get summary of all identified patterns."""
        return {
            "total_patterns": len(self.identified_patterns),
            "pattern_types": list({p.pattern_type for p in self.identified_patterns}),
            "patterns": [p.to_dict() for p in self.identified_patterns],
            "templates_analyzed": len(self.processed_templates),
        }

    def _create_template_from_data(
        self, extracted_data: Dict[str, Any], location: str, documents: List[str]
    ) -> ExpeditionTemplate:
        """Create ExpeditionTemplate from extracted data."""
        template = ExpeditionTemplate()

        # Basic information
        template.location = location
        template.sourceDocuments = documents
        template.extractionTimestamp = datetime.now()

        # Extract data with fallbacks
        template.date = extracted_data.get("date", datetime.now().strftime("%Y-%m-%d"))
        template.dayNumber = extracted_data.get("day_number", 1)
        template.weekday = extracted_data.get("weekday", "Unknown")
        template.notes = extracted_data.get("notes", "")

        # Equipment
        equipment = extracted_data.get("equipment", {})
        template.zodiacs = equipment.get("zodiacs", 0)
        template.twins = equipment.get("twins", 0)
        template.activityType = extracted_data.get("activity_type", "Unknown")

        # Groups
        groups_data = extracted_data.get("groups", [])
        for group_data in groups_data:
            from ..data.template_models import Group

            group = Group(
                groupName=group_data.get("groupName", "Unknown"),
                color=group_data.get("color", "Unknown"),
                departureTime=group_data.get("departureTime", "00:00"),
                returnTime=group_data.get("returnTime", "00:00"),
                activity=group_data.get("activity", "Unknown"),
            )
            template.groups.append(group)

        # Schedule
        schedule_data = extracted_data.get("schedule", [])
        for event_data in schedule_data:
            from ..data.template_models import ScheduleEvent

            event = ScheduleEvent(
                time=event_data.get("time", "00:00"),
                type=event_data.get("type", "unknown"),
                description=event_data.get("description", ""),
                location=event_data.get("location", location),
            )
            template.schedule.append(event)

        # Tides
        tides_data = extracted_data.get("tides", [])
        for tide_data in tides_data:
            from ..data.template_models import Tide

            tide = Tide(
                time=tide_data.get("time", "00:00"),
                height=tide_data.get("height", 0.0),
                label=tide_data.get("label", "unknown"),
            )
            template.tides.append(tide)

        return template

    def _apply_pattern_insights(
        self, template: ExpeditionTemplate, analysis: Dict[str, Any]
    ) -> ExpeditionTemplate:
        """Apply pattern analysis insights to the template."""
        try:
            # Add pattern insights to notes
            if analysis and "insights" in analysis:
                insights = analysis["insights"]
                if template.notes:
                    template.notes += f"\n\nPattern Analysis:\n{insights}"
                else:
                    template.notes = f"Pattern Analysis:\n{insights}"

            return template
        except Exception as e:
            logger.warning(f"Failed to apply pattern insights: {e}")
            return template

    def _calculate_completeness_score_from_data(
        self, extracted_data: Dict[str, Any]
    ) -> float:
        """Calculate completeness score based on extracted data."""
        try:
            required_fields = ["date", "location", "operation_type"]
            optional_fields = [
                "groups",
                "schedule",
                "tides",
                "equipment",
                "personnel",
                "notes",
            ]

            score = 0.0
            total_weight = len(required_fields) * 2 + len(
                optional_fields
            )  # Required fields worth 2x

            # Check required fields (worth 2 points each)
            for field in required_fields:
                if extracted_data.get(field):
                    score += 2.0

            # Check optional fields (worth 1 point each)
            for field in optional_fields:
                if extracted_data.get(field):
                    score += 1.0

            return min(score / total_weight, 1.0)
        except Exception as e:
            logger.warning(f"Failed to calculate completeness score: {e}")
            return 0.5  # Default score
