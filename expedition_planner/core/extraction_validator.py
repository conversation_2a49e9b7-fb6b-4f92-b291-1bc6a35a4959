"""
Extraction validator for ensuring high-quality data extraction.
Includes robust date parsing and confidence scoring.
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

# For robust date parsing
try:
    from dateutil import parser as date_parser

    HAS_DATEUTIL = True
except ImportError:
    date_parser = None
    HAS_DATEUTIL = False

logger = logging.getLogger(__name__)


class RobustDateParser:
    """Robust date parsing with context awareness and validation."""

    def __init__(self):
        """Initialize the date parser."""
        if not HAS_DATEUTIL:
            logger.warning(
                "dateutil not installed. Install with: pip install python-dateutil"
            )

    def extract_date_robust(self, text: str) -> Optional[str]:
        """
        Extract date from text with robust parsing.

        Args:
            text: Text to extract date from

        Returns:
            Extracted date in YYYY-MM-DD format or None if no valid date found
        """
        if not HAS_DATEUTIL:
            logger.error("dateutil required for robust date parsing")
            return None

        # Store all potential date matches with confidence scores
        potential_dates = []

        # Strategy 1: Look for explicit date patterns in context (highest confidence)
        date_contexts = [
            r"(?i)date[:\s]+([^\n]+)",
            r"(?i)on[:\s]+([^\n]+)",
            r"(?i)expedition[:\s]+([^\n]+)",
            r"(?i)scheduled[:\s]+([^\n]+)",
            r"(?i)planned[:\s]+([^\n]+)",
            r"(?i)departing[:\s]+([^\n]+)",
            r"(?i)arriving[:\s]+([^\n]+)",
        ]

        for pattern in date_contexts:
            match = re.search(pattern, text)
            if match:
                try:
                    # Use default=None to prevent defaulting to today's date
                    parsed = date_parser.parse(match.group(1), fuzzy=True, default=None)
                    if parsed and self.validate_date(parsed):
                        potential_dates.append(
                            {
                                "date": parsed.strftime("%Y-%m-%d"),
                                "confidence": 0.9,  # High confidence due to explicit context
                                "source": f"Context: {match.group(0)[:30]}...",
                            }
                        )
                except (ValueError, TypeError, AttributeError):
                    continue

        # Strategy 2: Look for date patterns line by line (medium confidence)
        for line in text.split("\n"):
            line = line.strip()
            if not line:
                continue

            # Skip lines that are too long (likely not a date)
            if len(line) > 50:
                continue

            # Skip lines with too many numbers (likely not a date)
            if sum(c.isdigit() for c in line) > 10:
                continue

            try:
                # Use default=None to prevent defaulting to today's date
                parsed = date_parser.parse(line, fuzzy=True, default=None)
                if parsed and self.validate_date(parsed):
                    potential_dates.append(
                        {
                            "date": parsed.strftime("%Y-%m-%d"),
                            "confidence": 0.7,  # Medium confidence
                            "source": f"Line: {line}",
                        }
                    )
            except (ValueError, TypeError, AttributeError):
                continue

        # Strategy 3: Look for common date formats in the entire text (medium-high confidence)
        date_patterns = [
            (
                r"\d{1,2}[/-]\d{1,2}[/-]\d{4}",
                0.8,
            ),  # DD/MM/YYYY or MM/DD/YYYY with 4-digit year (higher confidence)
            (
                r"\d{4}[/-]\d{1,2}[/-]\d{1,2}",
                0.85,
            ),  # YYYY/MM/DD (highest confidence for explicit format)
            (
                r"\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{4}",
                0.85,
            ),  # DD Mon YYYY with 4-digit year
            (
                r"(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{4}",
                0.85,
            ),  # Mon DD, YYYY with 4-digit year
            (
                r"\d{1,2}[/-]\d{1,2}[/-]\d{2}",
                0.6,
            ),  # DD/MM/YY or MM/DD/YY with 2-digit year (lower confidence)
            (
                r"\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2}",
                0.65,
            ),  # DD Mon YY with 2-digit year
            (
                r"(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{2}",
                0.65,
            ),  # Mon DD, YY with 2-digit year
            (
                r"\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*(?:\s+\d{4}|\s+\d{2})?",
                0.75,
            ),  # 18th March 2023 or 18th March
            (
                r"(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2}(?:st|nd|rd|th)?(?:\s+\d{4}|\s+\d{2})?",
                0.75,
            ),  # March 18th 2023 or March 18th
        ]

        for pattern, confidence in date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    # Use default=None to prevent defaulting to today's date
                    parsed = date_parser.parse(match, fuzzy=True, default=None)
                    if parsed and self.validate_date(parsed):
                        potential_dates.append(
                            {
                                "date": parsed.strftime("%Y-%m-%d"),
                                "confidence": confidence,
                                "source": f"Pattern: {match}",
                            }
                        )
                except (ValueError, TypeError, AttributeError):
                    continue

        # Strategy 4: Look for weekday + date patterns (high confidence)
        weekday_date_patterns = [
            r"(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday),?\s+\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*(?:\s+\d{4})?",
            r"(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s+\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*(?:\s+\d{4})?",
        ]

        for pattern in weekday_date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    # Use default=None to prevent defaulting to today's date
                    parsed = date_parser.parse(match, fuzzy=True, default=None)
                    if parsed and self.validate_date(parsed):
                        potential_dates.append(
                            {
                                "date": parsed.strftime("%Y-%m-%d"),
                                "confidence": 0.9,  # High confidence due to weekday + date format
                                "source": f"Weekday pattern: {match}",
                            }
                        )
                except (ValueError, TypeError, AttributeError):
                    continue

        # If we found potential dates, return the one with highest confidence
        if potential_dates:
            # Sort by confidence (descending)
            potential_dates.sort(key=lambda x: x["confidence"], reverse=True)

            # Only return dates with sufficient confidence (above 0.6)
            if potential_dates[0]["confidence"] >= 0.6:
                best_date = potential_dates[0]["date"]
                logger.info(
                    f"Selected date {best_date} with confidence {potential_dates[0]['confidence']} from {potential_dates[0]['source']}"
                )
                return best_date
            else:
                logger.warning(
                    f"Best date candidate has low confidence: {potential_dates[0]['confidence']}"
                )
                return None

        # No valid date found
        logger.warning("No valid date found in text")
        return None

    def validate_date(self, date_obj: datetime) -> bool:
        """
        Validate if the date is within a reasonable range.

        Args:
            date_obj: Datetime object to validate

        Returns:
            True if date is valid, False otherwise
        """
        # Reasonable date range for expeditions (adjust as needed)
        current_year = datetime.now().year
        return 2020 <= date_obj.year <= current_year + 2


class ExtractionValidator:
    """Validator for extracted data with confidence scoring."""

    def __init__(self):
        """Initialize the validator."""
        self.date_parser = RobustDateParser()
        self.confidence_threshold = 0.6  # 60% threshold

    def calculate_confidence(self, extracted_data: Dict[str, Any]) -> float:
        """
        Calculate confidence score for extracted data.

        Args:
            extracted_data: Extracted data dictionary

        Returns:
            Confidence score (0-1)
        """
        score = 0
        max_score = 100

        # Date validation (25 points)
        if extracted_data.get("date"):
            try:
                parsed_date = datetime.strptime(extracted_data["date"], "%Y-%m-%d")
                if 2020 <= parsed_date.year <= datetime.now().year + 2:
                    score += 25
            except Exception:
                pass

        # Location validation (25 points)
        location = extracted_data.get("location", "")
        if location and "unknown" not in location.lower():
            if len(location) > 5:  # Meaningful location name
                score += 25

        # Time validation (20 points)
        times = [
            extracted_data.get("arrival_time"),
            extracted_data.get("departure_time"),
        ]
        valid_times = 0
        for time_str in times:
            if time_str and re.match(r"^\d{1,2}:\d{2}$", time_str):
                valid_times += 1
        score += (valid_times / 2) * 20

        # Group/schedule validation (30 points)
        groups = extracted_data.get("groups", [])
        if isinstance(groups, list) and len(groups) > 0:
            score += 15

        schedule = extracted_data.get("schedule", [])
        if (
            isinstance(schedule, list) and len(schedule) > 2
        ):  # More than just arrival/departure
            score += 15

        return score / max_score

    def should_accept_extraction(self, extracted_data: Dict[str, Any]) -> bool:
        """
        Determine if extracted data should be accepted based on confidence score.

        Args:
            extracted_data: Extracted data dictionary

        Returns:
            True if extraction should be accepted, False otherwise
        """
        confidence = self.calculate_confidence(extracted_data)
        return confidence >= self.confidence_threshold

    def validate_and_enhance(
        self, extracted_data: Dict[str, Any], raw_text: str
    ) -> Dict[str, Any]:
        """
        Validate and enhance extracted data.

        Args:
            extracted_data: Extracted data dictionary
            raw_text: Raw text from which data was extracted

        Returns:
            Enhanced data dictionary with validation metadata
        """
        # Make a copy to avoid modifying the original
        enhanced_data = extracted_data.copy()

        # Track field confidence scores
        field_confidence = {}

        # Add validation metadata
        confidence = self.calculate_confidence(extracted_data)
        enhanced_data["_validation"] = {
            "confidence": confidence,
            "accepted": confidence >= self.confidence_threshold,
            "timestamp": datetime.now().isoformat(),
            "field_confidence": field_confidence,
            "extracted_fields": [],
            "default_fields": [],
            "enhanced_fields": [],
        }

        # Ensure required fields exist and have valid values
        # Fix missing or invalid date
        if not enhanced_data.get("date"):
            # Try to extract date using robust parser
            robust_date = self.date_parser.extract_date_robust(raw_text)
            if robust_date:
                enhanced_data["date"] = robust_date
                enhanced_data["_validation"]["enhanced_fields"].append("date")
                field_confidence["date"] = 0.8  # Good confidence from robust parser
            else:
                # If no date found, mark it as missing rather than using a default
                enhanced_data["date"] = None
                enhanced_data["_validation"]["default_fields"].append("date")
                field_confidence["date"] = 0.0  # No confidence
        else:
            # Date was already extracted
            enhanced_data["_validation"]["extracted_fields"].append("date")
            field_confidence["date"] = (
                0.9  # High confidence for directly extracted date
            )

        # Fix missing or invalid location
        if (
            not enhanced_data.get("location")
            or enhanced_data.get("location") == "Unknown Location"
        ):
            # Try to extract location from text
            location = self._extract_location_from_text(raw_text)
            if location and location != "Unknown Location":
                enhanced_data["location"] = location
                enhanced_data["_validation"]["enhanced_fields"].append("location")
                field_confidence["location"] = (
                    0.7  # Medium confidence for extracted location
                )
            else:
                enhanced_data["location"] = None  # Mark as truly missing
                enhanced_data["_validation"]["default_fields"].append("location")
                field_confidence["location"] = 0.0  # No confidence
        else:
            # Location was already extracted
            enhanced_data["_validation"]["extracted_fields"].append("location")
            field_confidence["location"] = (
                0.9  # High confidence for directly extracted location
            )

        # Ensure groups exist but don't add placeholders
        if not enhanced_data.get("groups"):
            enhanced_data["groups"] = []
            enhanced_data["_validation"]["default_fields"].append("groups")
            field_confidence["groups"] = 0.0
        else:
            enhanced_data["_validation"]["extracted_fields"].append("groups")
            field_confidence["groups"] = 0.8

        # Handle schedule with confidence tracking
        if (
            not enhanced_data.get("schedule")
            or len(enhanced_data.get("schedule", [])) < 1
        ):
            # Don't create a fake schedule, just leave it empty
            enhanced_data["schedule"] = []
            enhanced_data["_validation"]["default_fields"].append("schedule")
            field_confidence["schedule"] = 0.0
        else:
            # We have some schedule items, validate them
            valid_items = []
            for item in enhanced_data.get("schedule", []):
                if not isinstance(item, dict):
                    continue

                # Track if this item was modified
                item_modified = False

                # Ensure time field - this is required
                if "time" not in item or not item["time"]:
                    # Don't add items without times
                    continue

                # Ensure type field
                if "type" not in item or not item["type"]:
                    item["type"] = "activity"
                    item_modified = True

                # Handle description field - allow null values
                if "description" not in item:
                    # Only set minimal descriptions for arrival/departure
                    if item.get("type") == "arrival":
                        item["description"] = "Arrival"
                        item_modified = True
                    elif item.get("type") == "departure":
                        item["description"] = "Departure"
                        item_modified = True
                    else:
                        # Leave as null for other types
                        item["description"] = None

                # Add confidence metadata to the item
                item["_confidence"] = 0.9 if not item_modified else 0.6
                valid_items.append(item)

            # Update schedule with valid items only
            enhanced_data["schedule"] = valid_items

            if valid_items:
                enhanced_data["_validation"]["extracted_fields"].append("schedule")
                field_confidence["schedule"] = sum(
                    item.get("_confidence", 0) for item in valid_items
                ) / len(valid_items)
            else:
                enhanced_data["_validation"]["default_fields"].append("schedule")
                field_confidence["schedule"] = 0.0

        # Add extraction quality warnings
        warnings = []

        if enhanced_data.get("date") is None:
            warnings.append("No expedition date could be extracted")

        if enhanced_data.get("location") is None:
            warnings.append("No location could be extracted")

        if not enhanced_data.get("groups") or len(enhanced_data.get("groups", [])) == 0:
            warnings.append("No expedition groups identified")

        if (
            not enhanced_data.get("schedule")
            or len(enhanced_data.get("schedule", [])) == 0
        ):
            warnings.append("No schedule information could be extracted")
        elif len(enhanced_data.get("schedule", [])) < 3:
            warnings.append("Limited schedule information extracted")

        enhanced_data["_validation"]["warnings"] = warnings

        return enhanced_data

    def _extract_location_from_text(self, text: str) -> Optional[str]:
        """Extract location from text using pattern matching."""
        # Preprocess text to better isolate location names
        # Remove common non-location sections
        text = re.sub(r"(?i)weather\s*report.*?(?:\n|$)", " ", text)
        text = re.sub(r"(?i)equipment\s*list.*?(?:\n|$)", " ", text)

        # Common location patterns with higher precision
        location_patterns = [
            r"(?i)location[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
            r"(?i)destination[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
            r"(?i)site[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
            r"(?i)landing\s+at\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
            r"(?i)visit\s+to\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
            r"(?i)expedition\s+to\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
            r"(?i)arriving\s+at\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
        ]

        # Track confidence of location matches
        location_candidates = []

        # First pass: Look for explicit location markers (highest confidence)
        for pattern in location_patterns:
            match = re.search(pattern, text)
            if match:
                location = match.group(1).strip()
                # Validate it looks like a proper location name
                if len(location) > 3 and location[0].isupper():
                    location_candidates.append(
                        {
                            "location": location,
                            "confidence": 0.9,  # High confidence for explicit markers
                            "source": f"Pattern: {pattern}",
                        }
                    )

        # Second pass: Look for capitalized words that might be locations
        lines = text.split("\n")
        for line in lines:
            line = line.strip()
            # Skip long lines
            if len(line) > 50:
                continue

            # Look for capitalized words that might be a location
            words = line.split()
            if len(words) >= 2 and len(words) <= 5:
                # Check if first word is capitalized and not a common word
                if words[0][0].isupper() and words[0].lower() not in [
                    "the",
                    "a",
                    "an",
                    "to",
                    "from",
                    "in",
                    "at",
                    "by",
                    "on",
                    "expedition",
                    "group",
                    "team",
                    "schedule",
                    "weather",
                    "equipment",
                    "notes",
                    "time",
                    "date",
                    "day",
                ]:
                    # Check if it looks like a location name
                    potential_location = " ".join(words)
                    if re.match(
                        r"^[A-Z][a-z]+(\s+[A-Z][a-z]+){0,3}$", potential_location
                    ):
                        location_candidates.append(
                            {
                                "location": potential_location,
                                "confidence": 0.7,  # Medium confidence for capitalized words
                                "source": f"Line: {line}",
                            }
                        )

        # Third pass: Look for preposition + location patterns
        prep_location_patterns = [
            r"(?i)at\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
            r"(?i)in\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
            r"(?i)near\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
            r"(?i)to\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})",
        ]

        for pattern in prep_location_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                location = match.group(1).strip()
                # Validate it looks like a proper location name
                if len(location) > 3 and location[0].isupper():
                    # Check if it's not a common non-location word
                    if location.lower() not in [
                        "the",
                        "morning",
                        "afternoon",
                        "evening",
                        "night",
                        "today",
                        "tomorrow",
                    ]:
                        location_candidates.append(
                            {
                                "location": location,
                                "confidence": 0.75,  # Medium-high confidence
                                "source": f"Preposition pattern: {match.group(0)}",
                            }
                        )

        # If we found potential locations, return the one with highest confidence
        if location_candidates:
            # Sort by confidence (descending)
            location_candidates.sort(key=lambda x: x["confidence"], reverse=True)

            # Only return locations with sufficient confidence (above 0.7)
            if location_candidates[0]["confidence"] >= 0.7:
                best_location = location_candidates[0]["location"]
                logger.info(
                    f"Selected location {best_location} with confidence {location_candidates[0]['confidence']}"
                )
                return best_location
            else:
                logger.warning(
                    f"Best location candidate has low confidence: {location_candidates[0]['confidence']}"
                )
                return None

        # No valid location found
        logger.warning("No valid location found in text")
        return None
