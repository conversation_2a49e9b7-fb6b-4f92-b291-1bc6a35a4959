"""
LangChain-based expedition document processor.
"""

import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..agents.coordinator import ExpeditionCoordinator
from ..config.langchain_config import get_langchain_config
from ..utils.error_handlers import (
    ConfigurationError,
    LLMProcessingError,
    handle_expedition_errors,
    llm_error_handler,
    log_performance_metrics,
)

logger = logging.getLogger(__name__)


class LangChainExpeditionProcessor:
    """
    Main processor class that uses LangChain agents for intelligent expedition document analysis.
    """

    def __init__(self):
        """Initialize the LangChain expedition processor."""
        self.config = get_langchain_config()
        self.coordinator = ExpeditionCoordinator()
        self.processing_sessions = {}

        logger.info("LangChain expedition processor initialized")

    @handle_expedition_errors
    @log_performance_metrics
    def process_expedition(
        self,
        documents_directory: str,
        expedition_name: str,
        output_directory: Optional[str] = None,
        enable_analysis: bool = False,  # Default to False - focus on JSON generation
    ) -> Dict[str, Any]:
        """
        Process an entire expedition's documents using LangChain agents with focus on JSON generation.

        This method prioritizes creating clean, consistent JSON templates from expedition documents.
        Pattern analysis is optional and typically more effective when run separately on multiple
        JSON files from different operations.

        Args:
            documents_directory: Directory containing expedition documents
            expedition_name: Name of the expedition
            output_directory: Optional output directory (auto-generated if not provided)
            enable_analysis: Whether to include pattern analysis (default: False for better workflow)

        Returns:
            Dictionary containing processing results with focus on JSON template generation
        """
        try:
            # Generate session ID
            session_id = str(uuid.uuid4())

            # Create output directory if not provided
            if not output_directory:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_directory = f"expedition_output_{expedition_name}_{timestamp}"

            output_path = Path(output_directory)
            output_path.mkdir(parents=True, exist_ok=True)

            logger.info(f"Starting expedition processing: {expedition_name}")
            logger.info(f"Session ID: {session_id}")
            logger.info(f"Documents: {documents_directory}")
            logger.info(f"Output: {output_directory}")

            # Store session info
            self.processing_sessions[session_id] = {
                "expedition_name": expedition_name,
                "start_time": datetime.now(),
                "status": "processing",
                "documents_directory": documents_directory,
                "output_directory": output_directory,
            }

            # Process using coordinator
            result = self.coordinator.process_expedition_documents(
                documents_directory=documents_directory,
                output_directory=output_directory,
                expedition_name=expedition_name,
                enable_analysis=enable_analysis,
            )

            # Update session status
            self.processing_sessions[session_id]["status"] = result.get(
                "status", "unknown"
            )
            self.processing_sessions[session_id]["end_time"] = datetime.now()
            self.processing_sessions[session_id]["result"] = result

            # Ensure consistent return format with success field
            status = result.get("status", "unknown")
            success = status == "completed"

            # Create standardized response with expected field mappings
            original_summary = result.get("summary", {})
            standardized_summary = {
                "total_documents": original_summary.get("documents_processed", 1),
                "locations_processed": [result.get("expedition_name", expedition_name)],
                "json_files_generated": original_summary.get("json_files_generated", 0),
                "processing_time": original_summary.get("processing_time", "Unknown"),
            }

            standardized_result = {
                "success": success,
                "status": status,
                "expedition_name": expedition_name,
                "session_id": session_id,
                "summary": standardized_summary,
                "results": {
                    expedition_name: {
                        "json_files": [],
                        "analysis": "Processing completed",
                    }
                },
                "workflow_state": result,
            }

            logger.info(f"Expedition processing completed: {status}")
            return standardized_result

        except Exception as e:
            logger.error(f"Error processing expedition {expedition_name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "expedition_name": expedition_name,
                "session_id": session_id if "session_id" in locals() else None,
            }

    def process_document_folder(
        self, folder_path: str, grouping_method: str = "location"
    ) -> Dict[str, Any]:
        """
        Process a folder of documents with intelligent grouping.

        Args:
            folder_path: Path to folder containing documents
            grouping_method: Method to group documents ("location", "date", "operation_type")

        Returns:
            Dictionary containing organization and processing results
        """
        try:
            logger.info(f"Processing document folder: {folder_path}")

            # Use document organizer to scan and group documents
            organization_result = (
                self.coordinator.document_organizer.organize_documents(
                    folder_path, grouping_method
                )
            )

            if not organization_result.get("success"):
                return organization_result

            # Get processing recommendations
            document_groups = organization_result.get("groups", {})
            recommendations = (
                self.coordinator.document_organizer.recommend_processing_order(
                    document_groups
                )
            )

            return {
                "success": True,
                "organization": organization_result,
                "recommendations": recommendations,
                "next_steps": [
                    "Review document groups and recommendations",
                    "Process each group using process_document_group()",
                    "Generate final expedition reports",
                ],
            }

        except Exception as e:
            logger.error(f"Error processing document folder {folder_path}: {e}")
            return {"success": False, "error": str(e), "folder_path": folder_path}

    def process_document_group(
        self, document_paths: List[str], location: str, output_directory: str
    ) -> Dict[str, Any]:
        """
        Process a group of documents for a specific location.

        Args:
            document_paths: List of document file paths
            location: Location name for the group
            output_directory: Directory to save results

        Returns:
            Dictionary containing processing results
        """
        try:
            logger.info(f"Processing document group for location: {location}")

            result = self.coordinator.process_single_location(
                document_paths, location, output_directory
            )

            return result

        except Exception as e:
            logger.error(f"Error processing document group for {location}: {e}")
            return {"success": False, "error": str(e), "location": location}

    def analyze_patterns_only(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform pattern analysis on already extracted data.

        Args:
            extracted_data: Previously extracted expedition data

        Returns:
            Dictionary containing pattern analysis results
        """
        try:
            logger.info("Performing pattern analysis on provided data")

            # Use pattern analyzer
            analysis_result = (
                self.coordinator.pattern_analyzer.analyze_operational_patterns(
                    extracted_data
                )
            )

            if analysis_result.get("success"):
                # Generate comprehensive report
                report = (
                    self.coordinator.pattern_analyzer.create_pattern_analysis_report(
                        extracted_data
                    )
                )
                analysis_result["comprehensive_report"] = report

            return analysis_result

        except Exception as e:
            logger.error(f"Error analyzing patterns: {e}")
            return {"success": False, "error": str(e)}

    def analyze_multiple_json_files(self, json_file_paths: List[str]) -> Dict[str, Any]:
        """
        Analyze patterns across multiple JSON files - the recommended approach for pattern analysis.

        This method loads multiple JSON files from different expedition operations and performs
        comparative analysis to discover meaningful operational patterns.

        Args:
            json_file_paths: List of paths to JSON files from different expedition operations

        Returns:
            Dictionary containing comprehensive pattern analysis results
        """
        try:
            logger.info(f"Analyzing patterns across {len(json_file_paths)} JSON files")

            if len(json_file_paths) < 2:
                logger.warning(
                    "Pattern analysis is most effective with 2 or more operations"
                )

            # Load and combine JSON data
            combined_data = {"days": []}
            loaded_files = []

            for file_path in json_file_paths:
                try:
                    with open(file_path, encoding="utf-8") as f:
                        json_data = json.load(f)

                    # Extract days from the JSON structure
                    if "days" in json_data:
                        combined_data["days"].extend(json_data["days"])
                        loaded_files.append(file_path)
                    elif isinstance(json_data, dict) and "location" in json_data:
                        # If it's a single day object, add it
                        combined_data["days"].append(json_data)
                        loaded_files.append(file_path)
                    else:
                        logger.warning(f"Unrecognized JSON structure in {file_path}")

                except Exception as e:
                    logger.warning(f"Failed to load JSON file {file_path}: {e}")
                    continue

            if not combined_data["days"]:
                return {
                    "success": False,
                    "error": "No valid expedition data found in JSON files",
                    "files_attempted": len(json_file_paths),
                    "files_loaded": 0,
                }

            logger.info(
                f"Successfully loaded {len(loaded_files)} files with {len(combined_data['days'])} operation days"
            )

            # Perform comprehensive pattern analysis
            analysis_result = (
                self.coordinator.pattern_analyzer.analyze_operational_patterns(
                    combined_data
                )
            )

            # Generate detailed report
            pattern_report = ""
            if analysis_result.get("success"):
                pattern_report = (
                    self.coordinator.pattern_analyzer.create_pattern_analysis_report(
                        combined_data
                    )
                )

            return {
                "success": analysis_result.get("success", False),
                "analysis_result": analysis_result,
                "pattern_report": pattern_report,
                "files_analyzed": len(loaded_files),
                "files_attempted": len(json_file_paths),
                "days_analyzed": len(combined_data["days"]),
                "loaded_files": loaded_files,
                "analysis_effectiveness": "high"
                if len(combined_data["days"]) >= 3
                else "moderate"
                if len(combined_data["days"]) >= 2
                else "low",
            }

        except Exception as e:
            logger.error(f"Error analyzing multiple JSON files: {e}")
            return {
                "success": False,
                "error": str(e),
                "files_attempted": len(json_file_paths) if json_file_paths else 0,
            }

    def generate_json_templates_only(
        self,
        extracted_data: Dict[str, Any],
        pattern_analysis: str,
        output_directory: str,
    ) -> Dict[str, Any]:
        """
        Generate JSON templates from extracted data and pattern analysis.

        Args:
            extracted_data: Extracted expedition data
            pattern_analysis: Pattern analysis text to include
            output_directory: Directory to save JSON files

        Returns:
            Dictionary containing generation results
        """
        try:
            logger.info("Generating JSON templates from provided data")

            result = self.coordinator.json_generator.generate_json_templates(
                extracted_data, pattern_analysis, output_directory
            )

            return result

        except Exception as e:
            logger.error(f"Error generating JSON templates: {e}")
            return {"success": False, "error": str(e)}

    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """
        Get status of a processing session.

        Args:
            session_id: Session identifier

        Returns:
            Dictionary containing session status
        """
        if session_id in self.processing_sessions:
            session = self.processing_sessions[session_id]

            # Calculate processing time if completed
            processing_time = None
            if "end_time" in session:
                duration = session["end_time"] - session["start_time"]
                processing_time = str(duration)

            # Handle datetime conversion safely
            start_time = session.get("start_time")
            end_time = session.get("end_time")

            start_time_iso = None
            if start_time:
                if hasattr(start_time, "isoformat"):
                    start_time_iso = start_time.isoformat()
                else:
                    start_time_iso = str(start_time)

            end_time_iso = None
            if end_time:
                if hasattr(end_time, "isoformat"):
                    end_time_iso = end_time.isoformat()
                else:
                    end_time_iso = str(end_time)

            return {
                "session_id": session_id,
                "expedition_name": session.get("expedition_name"),
                "status": session.get("status"),
                "start_time": start_time_iso,
                "end_time": end_time_iso,
                "processing_time": processing_time,
                "documents_directory": session.get("documents_directory"),
                "output_directory": session.get("output_directory"),
            }
        else:
            return {"error": "Session not found", "session_id": session_id}

    def list_active_sessions(self) -> List[Dict[str, Any]]:
        """
        List all active processing sessions.

        Returns:
            List of session information dictionaries
        """
        sessions = []

        for session_id, session_info in self.processing_sessions.items():
            sessions.append(
                {
                    "session_id": session_id,
                    "expedition_name": session_info.get("expedition_name"),
                    "status": session_info.get("status"),
                    "start_time": session_info.get("start_time").isoformat()
                    if session_info.get("start_time")
                    else None,
                }
            )

        return sessions

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Get processor capabilities and supported features.

        Returns:
            Dictionary containing processor capabilities
        """
        return {
            "supported_formats": self.config["files"]["supported_extensions"],
            "llm_model": self.config["ollama"]["model"],
            "offline_operation": True,
            "agents": [
                "Document Organizer Agent",
                "Data Extraction Agent",
                "Pattern Analysis Agent",
                "JSON Generator Agent",
                "Coordinator Agent",
            ],
        }

    def get_processor_capabilities(self) -> Dict[str, Any]:
        """
        Get information about processor capabilities.

        Returns:
            Dictionary describing processor capabilities
        """
        return {
            "processor_type": "LangChain Agent-Based",
            "version": "1.0.0",
            "capabilities": [
                "Intelligent document organization by location/operation site",
                "Structured data extraction using Docling + LLM",
                "Operational pattern analysis and insights",
                "Multiple JSON generation for different operation types",
                "AM-only, PM-only, and combined operation support",
                "Pattern analysis integration in JSON templates",
                "Comprehensive validation and error handling",
                "Detailed logging of agent decision-making processes",
            ],
            "supported_formats": self.config["files"]["supported_extensions"],
            "llm_model": self.config["ollama"]["model"],
            "offline_operation": True,
            "agents": [
                "Document Organizer Agent",
                "Data Extraction Agent",
                "Pattern Analysis Agent",
                "JSON Generator Agent",
                "Coordinator Agent",
            ],
        }

    def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate processor configuration and dependencies.

        Returns:
            Dictionary containing validation results
        """
        validation_results = {"valid": True, "issues": [], "warnings": []}

        try:
            # Check Ollama connection with timeout
            try:
                import time

                import requests

                # First check if Ollama server is responding
                start_time = time.time()
                try:
                    response = requests.get(
                        f"{self.config['ollama']['base_url']}/api/tags", timeout=10
                    )
                    if response.status_code == 200:
                        validation_results["ollama_status"] = "connected"
                        # Quick test with minimal prompt
                        from langchain_ollama import OllamaLLM

                        llm = OllamaLLM(
                            model=self.config["ollama"]["model"],
                            base_url=self.config["ollama"]["base_url"],
                            timeout=30,  # Short timeout for validation
                        )
                        # Very short test prompt
                        result = llm.invoke("Hi")
                        if len(result) > 0:
                            validation_results["ollama_status"] = "connected"
                        else:
                            validation_results["warnings"].append(
                                "Ollama responded but with empty result"
                            )
                    else:
                        validation_results["warnings"].append(
                            f"Ollama server returned status {response.status_code}"
                        )
                        validation_results["ollama_status"] = "warning"
                except requests.exceptions.RequestException as e:
                    validation_results["warnings"].append(
                        f"Ollama connection issue: {e!s}"
                    )
                    validation_results["ollama_status"] = "disconnected"

            except Exception as e:
                validation_results["warnings"].append(f"Ollama validation error: {e!s}")
                validation_results["ollama_status"] = "error"

            # Check Docling availability
            try:
                from docling.document_converter import DocumentConverter

                validation_results["docling_status"] = "available"
            except Exception as e:
                validation_results["valid"] = False
                validation_results["issues"].append(f"Docling not available: {e!s}")
                validation_results["docling_status"] = "unavailable"

            # Check configuration completeness
            required_config_keys = ["ollama", "agents", "patterns", "templates"]
            for key in required_config_keys:
                if key not in self.config:
                    validation_results["warnings"].append(
                        f"Missing configuration section: {key}"
                    )

        except Exception as e:
            validation_results["valid"] = False
            validation_results["issues"].append(
                f"Configuration validation error: {e!s}"
            )

        return validation_results
