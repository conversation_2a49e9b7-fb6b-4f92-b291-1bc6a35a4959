"""
Robust OCR implementation with preprocessing, multiple engines, and confidence scoring.
"""

import logging
import os
import re
import tempfile
from pathlib import Path
from typing import Dict, List, NamedTuple, Optional, Tuple, Union

# Optional imports for OCR and image processing
try:
    import cv2
    import numpy as np

    HAS_CV2 = True
except ImportError:
    cv2 = None
    np = None
    HAS_CV2 = False

try:
    import pytesseract
    from PIL import Image, ImageEnhance, ImageFilter

    HAS_TESSERACT = True
except ImportError:
    pytesseract = None
    Image = None
    ImageEnhance = None
    ImageFilter = None
    HAS_TESSERACT = False

try:
    import easyocr

    HAS_EASYOCR = True
except ImportError:
    easyocr = None
    HAS_EASYOCR = False

from ..utils.performance import memory_efficient, performance_monitor

logger = logging.getLogger(__name__)


class OCRResult(NamedTuple):
    """Result from OCR extraction with text and confidence score."""

    text: str
    confidence: float
    engine: str
    preprocessing: str


class ExtractionError(Exception):
    """Exception raised when OCR extraction fails or has poor quality."""



class TesseractOCR:
    """OCR extraction using Tesseract."""

    def __init__(self):
        """Initialize Tesseract OCR engine."""
        if not HAS_TESSERACT:
            raise ImportError(
                "Tesseract OCR dependencies not installed. Install with: pip install pytesseract pillow"
            )

        # Check if tesseract is installed
        try:
            pytesseract.get_tesseract_version()
        except Exception as e:
            raise ImportError(f"Tesseract not properly installed or not in PATH: {e}")

    @performance_monitor
    def extract(self, image) -> OCRResult:
        """
        Extract text from image using Tesseract.

        Args:
            image: PIL Image or numpy array

        Returns:
            OCRResult with extracted text and confidence
        """
        try:
            # Convert numpy array to PIL Image if needed
            if isinstance(image, np.ndarray):
                image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

            # Get text
            text = pytesseract.image_to_string(image)

            # Get confidence data
            data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)

            # Calculate average confidence
            confidences = [float(conf) for conf in data["conf"] if conf != "-1"]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0

            # Normalize confidence to 0-1 range
            confidence = avg_confidence / 100.0

            return OCRResult(
                text=text,
                confidence=confidence,
                engine="tesseract",
                preprocessing="none",
            )

        except Exception as e:
            logger.error(f"Tesseract OCR extraction failed: {e}")
            return OCRResult(
                text="", confidence=0.0, engine="tesseract", preprocessing="none"
            )


class EasyOCR:
    """OCR extraction using EasyOCR."""

    def __init__(self):
        """Initialize EasyOCR engine."""
        if not HAS_EASYOCR:
            raise ImportError(
                "EasyOCR dependencies not installed. Install with: pip install easyocr"
            )

        # Initialize reader (lazy loading - will only load when first used)
        self._reader = None

    @property
    def reader(self):
        """Lazy load the EasyOCR reader."""
        if self._reader is None:
            self._reader = easyocr.Reader(["en"])
        return self._reader

    @performance_monitor
    @memory_efficient(max_memory_mb=1000.0)
    def extract(self, image) -> OCRResult:
        """
        Extract text from image using EasyOCR.

        Args:
            image: PIL Image or numpy array

        Returns:
            OCRResult with extracted text and confidence
        """
        try:
            # Convert PIL Image to numpy array if needed
            if isinstance(image, Image.Image):
                image = np.array(image)

            # Get results
            results = self.reader.readtext(image)

            # Extract text and confidence
            texts = []
            confidences = []

            for _, text, confidence in results:
                texts.append(text)
                confidences.append(confidence)

            # Combine text and calculate average confidence
            full_text = " ".join(texts)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0

            return OCRResult(
                text=full_text,
                confidence=avg_confidence,
                engine="easyocr",
                preprocessing="none",
            )

        except Exception as e:
            logger.error(f"EasyOCR extraction failed: {e}")
            return OCRResult(
                text="", confidence=0.0, engine="easyocr", preprocessing="none"
            )


class RobustOCRExtractor:
    """
    Robust OCR extraction with preprocessing, multiple engines, and confidence scoring.
    """

    def __init__(self):
        """Initialize OCR extractors and preprocessing."""
        self.engines = {}

        # Initialize Tesseract if available
        try:
            if HAS_TESSERACT:
                self.engines["tesseract"] = TesseractOCR()
                logger.info("Tesseract OCR engine initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Tesseract OCR: {e}")

        # Initialize EasyOCR if available
        try:
            if HAS_EASYOCR:
                self.engines["easyocr"] = EasyOCR()
                logger.info("EasyOCR engine initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize EasyOCR: {e}")

        # Check if we have at least one OCR engine
        if not self.engines:
            logger.error("No OCR engines available. Install pytesseract or easyocr")

        # Preprocessing options
        self.preprocessing_methods = {
            "none": self._no_preprocessing,
            "basic": self._basic_preprocessing,
            "advanced": self._advanced_preprocessing,
            "contrast_enhancement": self._contrast_enhancement_preprocessing,
            "morphological": self._morphological_preprocessing,
            "binarization": self._binarization_preprocessing,
            "edge_enhancement": self._edge_enhancement_preprocessing,
        }

        # Default confidence threshold
        self.confidence_threshold = 0.7

    def _no_preprocessing(self, image):
        """No preprocessing, return image as is."""
        return image

    def _basic_preprocessing(self, image):
        """
        Basic image preprocessing for better OCR.

        Args:
            image: PIL Image or numpy array

        Returns:
            Preprocessed image
        """
        if not HAS_CV2:
            return image

        try:
            # Convert PIL Image to numpy array if needed
            if isinstance(image, Image.Image):
                image = np.array(image)
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Apply thresholding
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            return thresh

        except Exception as e:
            logger.error(f"Basic preprocessing failed: {e}")
            return image

    def _advanced_preprocessing(self, image):
        """
        Advanced image preprocessing for better OCR.

        Args:
            image: PIL Image or numpy array

        Returns:
            Preprocessed image
        """
        if not HAS_CV2:
            return image

        try:
            # Convert PIL Image to numpy array if needed
            if isinstance(image, Image.Image):
                image = np.array(image)
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Apply noise reduction
            denoised = cv2.fastNlMeansDenoising(gray, None, 10, 7, 21)

            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )

            # Deskew if needed
            angle = self._get_skew_angle(thresh)
            if abs(angle) > 0.5:
                deskewed = self._deskew(thresh, angle)
                return deskewed

            return thresh

        except Exception as e:
            logger.error(f"Advanced preprocessing failed: {e}")
            return image

    def _contrast_enhancement_preprocessing(self, image):
        """
        Enhance contrast for better text recognition.

        Args:
            image: PIL Image or numpy array

        Returns:
            Preprocessed image
        """
        if not HAS_CV2:
            return image

        try:
            # Convert PIL Image to numpy array if needed
            if isinstance(image, Image.Image):
                image = np.array(image)
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)

            # Apply slight Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(enhanced, (3, 3), 0)

            # Apply Otsu's thresholding
            _, thresh = cv2.threshold(
                blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
            )

            return thresh

        except Exception as e:
            logger.error(f"Contrast enhancement preprocessing failed: {e}")
            return image

    def _morphological_preprocessing(self, image):
        """
        Apply morphological operations to enhance text.

        Args:
            image: PIL Image or numpy array

        Returns:
            Preprocessed image
        """
        if not HAS_CV2:
            return image

        try:
            # Convert PIL Image to numpy array if needed
            if isinstance(image, Image.Image):
                image = np.array(image)
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # Apply thresholding
            _, thresh = cv2.threshold(
                blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
            )

            # Create a kernel for morphological operations
            kernel = np.ones((1, 1), np.uint8)

            # Apply erosion to make text thinner and clearer
            eroded = cv2.erode(thresh, kernel, iterations=1)

            # Apply dilation to connect broken text
            dilated = cv2.dilate(eroded, kernel, iterations=1)

            return dilated

        except Exception as e:
            logger.error(f"Morphological preprocessing failed: {e}")
            return image

    def _binarization_preprocessing(self, image):
        """
        Apply advanced binarization techniques.

        Args:
            image: PIL Image or numpy array

        Returns:
            Preprocessed image
        """
        if not HAS_CV2:
            return image

        try:
            # Convert PIL Image to numpy array if needed
            if isinstance(image, Image.Image):
                image = np.array(image)
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Apply bilateral filter to preserve edges while reducing noise
            bilateral = cv2.bilateralFilter(gray, 9, 75, 75)

            # Apply Sauvola thresholding (local adaptive thresholding)
            # Simulate Sauvola using adaptive threshold with parameters tuned for text
            thresh = cv2.adaptiveThreshold(
                bilateral, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 8
            )

            return thresh

        except Exception as e:
            logger.error(f"Binarization preprocessing failed: {e}")
            return image

    def _edge_enhancement_preprocessing(self, image):
        """
        Enhance edges for better text recognition.

        Args:
            image: PIL Image or numpy array

        Returns:
            Preprocessed image
        """
        if not HAS_CV2:
            return image

        try:
            # Convert PIL Image to numpy array if needed
            if isinstance(image, Image.Image):
                image = np.array(image)
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)

            # Apply Canny edge detection
            edges = cv2.Canny(blurred, 100, 200)

            # Dilate edges to connect text
            kernel = np.ones((2, 2), np.uint8)
            dilated_edges = cv2.dilate(edges, kernel, iterations=1)

            # Invert for better OCR (white text on black background)
            inverted = cv2.bitwise_not(dilated_edges)

            return inverted

        except Exception as e:
            logger.error(f"Edge enhancement preprocessing failed: {e}")
            return image

    def _get_skew_angle(self, image):
        """
        Calculate skew angle of the image.

        Args:
            image: Grayscale image

        Returns:
            Skew angle in degrees
        """
        try:
            # Find all non-zero points
            coords = np.column_stack(np.where(image > 0))

            # Find the minimum area rectangle
            rect = cv2.minAreaRect(coords)

            # Get the angle
            angle = rect[2]

            # Adjust angle
            if angle < -45:
                angle = 90 + angle

            return angle

        except Exception as e:
            logger.error(f"Failed to get skew angle: {e}")
            return 0

    def _deskew(self, image, angle):
        """
        Deskew the image by rotating it.

        Args:
            image: Image to deskew
            angle: Skew angle in degrees

        Returns:
            Deskewed image
        """
        try:
            # Get image dimensions
            (h, w) = image.shape[:2]
            center = (w // 2, h // 2)

            # Get rotation matrix
            M = cv2.getRotationMatrix2D(center, angle, 1.0)

            # Rotate the image
            rotated = cv2.warpAffine(
                image, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE
            )

            return rotated

        except Exception as e:
            logger.error(f"Failed to deskew image: {e}")
            return image

    @performance_monitor
    def preprocess_image(self, image_path, method="advanced"):
        """
        Preprocess image for better OCR.

        Args:
            image_path: Path to image file
            method: Preprocessing method ("none", "basic", or "advanced")

        Returns:
            Preprocessed image
        """
        try:
            # Check if preprocessing method exists
            if method not in self.preprocessing_methods:
                logger.warning(f"Unknown preprocessing method: {method}. Using 'none'")
                method = "none"

            # Load image
            if isinstance(image_path, (str, Path)):
                if HAS_CV2:
                    image = cv2.imread(str(image_path))
                else:
                    image = Image.open(str(image_path))
            else:
                # Assume it's already an image
                image = image_path

            # Apply preprocessing
            preprocessed = self.preprocessing_methods[method](image)

            return preprocessed

        except Exception as e:
            logger.error(f"Image preprocessing failed: {e}")
            if isinstance(image_path, (str, Path)):
                # Return original image if preprocessing fails
                if HAS_CV2:
                    return cv2.imread(str(image_path))
                else:
                    return Image.open(str(image_path))
            return image_path

    @performance_monitor
    def extract_with_quality_check(self, image_path, min_confidence=None):
        """
        Extract text from image with quality check.

        Args:
            image_path: Path to image file
            min_confidence: Minimum confidence threshold (0-1)

        Returns:
            Extracted text

        Raises:
            ExtractionError: If OCR quality is too poor
        """
        if not self.engines:
            raise ExtractionError("No OCR engines available")

        if min_confidence is None:
            min_confidence = self.confidence_threshold

        # Try all preprocessing methods
        preprocessing_methods = list(self.preprocessing_methods.keys())

        all_results = []
        extraction_metadata = {
            "attempts": [],
            "best_confidence": 0.0,
            "best_method": None,
            "best_engine": None,
            "text_length": 0,
        }

        for method in preprocessing_methods:
            # Preprocess image
            processed_img = self.preprocess_image(image_path, method)

            # Try each OCR engine
            for engine_name, engine in self.engines.items():
                try:
                    result = engine.extract(processed_img)

                    # Create a new result with the preprocessing method
                    result = OCRResult(
                        text=result.text,
                        confidence=result.confidence,
                        engine=engine_name,
                        preprocessing=method,
                    )

                    # Add to results only if text is not empty
                    if result.text and len(result.text.strip()) > 0:
                        all_results.append(result)

                        # Record attempt metadata
                        attempt_info = {
                            "engine": engine_name,
                            "preprocessing": method,
                            "confidence": result.confidence,
                            "text_length": len(result.text),
                            "success": True,
                        }
                        extraction_metadata["attempts"].append(attempt_info)

                        logger.info(
                            f"OCR extraction with {engine_name} ({method}): confidence={result.confidence:.2f}, length={len(result.text)}"
                        )
                    else:
                        # Record failed attempt
                        attempt_info = {
                            "engine": engine_name,
                            "preprocessing": method,
                            "confidence": 0.0,
                            "text_length": 0,
                            "success": False,
                            "reason": "Empty text result",
                        }
                        extraction_metadata["attempts"].append(attempt_info)
                        logger.warning(
                            f"OCR extraction with {engine_name} ({method}) returned empty text"
                        )

                except Exception as e:
                    # Record failed attempt
                    attempt_info = {
                        "engine": engine_name,
                        "preprocessing": method,
                        "confidence": 0.0,
                        "text_length": 0,
                        "success": False,
                        "reason": str(e),
                    }
                    extraction_metadata["attempts"].append(attempt_info)
                    logger.error(
                        f"OCR extraction with {engine_name} ({method}) failed: {e}"
                    )

        if not all_results:
            logger.error("All OCR extraction attempts failed")
            logger.debug(f"Extraction metadata: {extraction_metadata}")
            raise ExtractionError("All OCR extraction attempts failed")

        # Find the best result based on confidence and text length
        # We'll use a weighted score that considers both confidence and text length
        for result in all_results:
            # Calculate a score that weights confidence more heavily but also considers text length
            # This helps avoid high-confidence but very short results
            text_length_score = min(
                1.0, len(result.text) / 1000
            )  # Normalize text length (cap at 1000 chars)
            combined_score = (result.confidence * 0.8) + (
                text_length_score * 0.2
            )  # 80% confidence, 20% length

            # Store the result's score for sorting
            result = result._replace(confidence=combined_score)

        # Sort by the combined score
        best_result = max(all_results, key=lambda r: r.confidence)

        # Update metadata
        extraction_metadata["best_confidence"] = best_result.confidence
        extraction_metadata["best_method"] = best_result.preprocessing
        extraction_metadata["best_engine"] = best_result.engine
        extraction_metadata["text_length"] = len(best_result.text)

        logger.info(
            f"Best OCR result: {best_result.engine} with {best_result.preprocessing} preprocessing, confidence={best_result.confidence:.2f}, length={len(best_result.text)}"
        )

        # Check confidence
        if best_result.confidence < min_confidence:
            logger.warning(
                f"OCR quality too poor: {best_result.confidence:.2f} < {min_confidence}"
            )

            # Try to combine results from multiple methods to improve quality
            combined_text = self._combine_ocr_results(all_results)
            if combined_text and len(combined_text) > len(best_result.text):
                logger.info(
                    f"Using combined OCR result: {len(combined_text)} characters"
                )
                return combined_text

            # If combination didn't help, raise error
            logger.debug(f"Extraction metadata: {extraction_metadata}")
            raise ExtractionError(f"OCR quality too poor: {best_result.confidence:.2f}")

        return best_result.text

    def _combine_ocr_results(self, results):
        """
        Combine multiple OCR results to improve quality.

        Args:
            results: List of OCRResult objects

        Returns:
            Combined text
        """
        if not results:
            return None

        # Sort results by confidence (highest first)
        sorted_results = sorted(results, key=lambda r: r.confidence, reverse=True)

        # Start with the highest confidence result
        combined_text = sorted_results[0].text

        # Function to calculate text similarity
        def similarity(text1, text2):
            # Simple Jaccard similarity of words
            words1 = set(text1.lower().split())
            words2 = set(text2.lower().split())

            if not words1 or not words2:
                return 0.0

            intersection = words1.intersection(words2)
            union = words1.union(words2)

            return len(intersection) / len(union)

        # Try to add unique content from other results
        for result in sorted_results[1:]:
            # Skip if similarity is too high (likely duplicate content)
            if similarity(combined_text, result.text) > 0.8:
                continue

            # Add unique paragraphs
            for paragraph in result.text.split("\n\n"):
                paragraph = paragraph.strip()
                if not paragraph:
                    continue

                # Check if this paragraph is unique
                is_unique = True
                for existing_paragraph in combined_text.split("\n\n"):
                    existing_paragraph = existing_paragraph.strip()
                    if similarity(paragraph, existing_paragraph) > 0.6:
                        is_unique = False
                        break

                if is_unique:
                    combined_text += f"\n\n{paragraph}"

        return combined_text

    @performance_monitor
    def extract_from_pdf_page(self, pdf_path, page_num, min_confidence=None):
        """
        Extract text from a PDF page using OCR.

        Args:
            pdf_path: Path to PDF file
            page_num: Page number (0-based)
            min_confidence: Minimum confidence threshold (0-1)

        Returns:
            Extracted text

        Raises:
            ExtractionError: If OCR quality is too poor
        """
        try:
            import fitz  # PyMuPDF

            # Open PDF
            doc = fitz.open(str(pdf_path))

            # Check page number
            if page_num < 0 or page_num >= len(doc):
                raise ValueError(f"Invalid page number: {page_num}")

            # Get page
            page = doc.load_page(page_num)

            # Render page to image
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better OCR

            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp:
                pix.save(tmp.name)
                tmp_path = tmp.name

            try:
                # Extract text with OCR
                text = self.extract_with_quality_check(tmp_path, min_confidence)
                return text
            finally:
                # Clean up temporary file
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)

        except Exception as e:
            logger.error(f"Failed to extract text from PDF page: {e}")
            raise ExtractionError(f"Failed to extract text from PDF page: {e}")
