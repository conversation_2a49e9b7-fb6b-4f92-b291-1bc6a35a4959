"""
Comprehensive JSON validation and sanitization utilities for expedition planner.
Handles malformed JSON, control characters, and structural issues.
"""

import json
import logging
import re
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class JSONValidator:
    """Comprehensive JSON validator with sanitization and repair capabilities."""

    def __init__(self):
        """Initialize the JSON validator."""
        self.validation_history = []
        self.repair_strategies = [
            self._strategy_direct_parse,
            self._strategy_sanitize_and_parse,
            self._strategy_repair_structure,
            self._strategy_extract_and_repair,
            self._strategy_intelligent_reconstruction,
        ]

    def validate_and_repair(
        self, json_string: str, context: str = ""
    ) -> Dict[str, Any]:
        """
        Validate and repair JSON string with comprehensive error handling.

        Args:
            json_string: The JSON string to validate and repair
            context: Context information for better error reporting

        Returns:
            Dictionary with validation results and repaired JSON
        """
        result = {
            "is_valid": False,
            "original_string": json_string[:200] + "..."
            if len(json_string) > 200
            else json_string,
            "repaired_json": None,
            "strategy_used": None,
            "errors": [],
            "warnings": [],
            "context": context,
        }

        # Try each repair strategy
        for i, strategy in enumerate(self.repair_strategies):
            try:
                logger.debug(
                    f"Trying JSON repair strategy {i + 1}: {strategy.__name__}"
                )

                repaired_json = strategy(json_string)
                if repaired_json is not None:
                    # Validate the repaired JSON
                    validation_result = self._validate_json_structure(repaired_json)

                    if validation_result["is_valid"]:
                        result.update(
                            {
                                "is_valid": True,
                                "repaired_json": repaired_json,
                                "strategy_used": strategy.__name__,
                                "warnings": validation_result.get("warnings", []),
                            }
                        )
                        logger.info(
                            f"JSON successfully repaired using {strategy.__name__}"
                        )
                        return result
                    else:
                        result["errors"].extend(validation_result.get("errors", []))

            except Exception as e:
                logger.debug(f"Strategy {strategy.__name__} failed: {e}")
                result["errors"].append(f"Strategy {strategy.__name__}: {e!s}")
                continue

        # All strategies failed
        logger.warning(f"All JSON repair strategies failed for context: {context}")
        result["errors"].append("All repair strategies failed")
        return result

    def _strategy_direct_parse(self, json_string: str) -> Optional[Dict[str, Any]]:
        """Strategy 1: Try direct JSON parsing."""
        return json.loads(json_string.strip())

    def _strategy_sanitize_and_parse(
        self, json_string: str
    ) -> Optional[Dict[str, Any]]:
        """Strategy 2: Sanitize control characters and parse."""
        sanitized = self._sanitize_control_characters(json_string)
        return json.loads(sanitized)

    def _strategy_repair_structure(self, json_string: str) -> Optional[Dict[str, Any]]:
        """Strategy 3: Repair common structural issues."""
        repaired = self._repair_json_structure(json_string)
        return json.loads(repaired)

    def _strategy_extract_and_repair(
        self, json_string: str
    ) -> Optional[Dict[str, Any]]:
        """Strategy 4: Extract JSON from text and repair."""
        extracted = self._extract_json_from_text(json_string)
        if extracted:
            repaired = self._repair_json_structure(extracted)
            return json.loads(repaired)
        return None

    def _strategy_intelligent_reconstruction(
        self, json_string: str
    ) -> Optional[Dict[str, Any]]:
        """Strategy 5: Intelligently reconstruct JSON from fragments."""
        return self._reconstruct_json_from_fragments(json_string)

    def _sanitize_control_characters(self, text: str) -> str:
        """Remove or replace control characters that break JSON."""
        # Remove null bytes and other problematic control characters
        text = text.replace("\x00", "")  # Null byte
        text = text.replace("\x08", "")  # Backspace
        text = text.replace("\x0c", "")  # Form feed
        text = text.replace("\x0b", "")  # Vertical tab

        # Replace problematic characters with safe alternatives
        text = text.replace("\r\n", "\\n")  # Windows line endings
        text = text.replace("\r", "\\n")  # Mac line endings
        text = text.replace("\n", "\\n")  # Unix line endings
        text = text.replace("\t", "\\t")  # Tabs

        # Remove other control characters (except allowed ones)
        text = re.sub(r"[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]", "", text)

        return text

    def _repair_json_structure(self, text: str) -> str:
        """Repair common JSON structural issues."""
        # Remove trailing commas
        text = re.sub(r",(\s*[}\]])", r"\1", text)

        # Fix unquoted keys
        text = re.sub(r"(\s*)(\w+)(\s*):", r'\1"\2"\3:', text)

        # Fix single quotes to double quotes (carefully)
        text = self._fix_quote_issues(text)

        # Balance brackets and braces
        text = self._balance_brackets(text)

        # Ensure proper JSON wrapper
        text = self._ensure_json_wrapper(text)

        return text

    def _fix_quote_issues(self, text: str) -> str:
        """Fix quote-related JSON issues."""
        # Protect contractions and possessives
        text = re.sub(r"(\w)'(s|t|re|ve|ll|d)\b", r"\1APOSTROPHE\2", text)

        # Replace remaining single quotes with double quotes
        text = text.replace("'", '"')

        # Restore contractions
        text = text.replace("APOSTROPHE", "'")

        # Fix escaped quotes
        text = re.sub(r'\\"+', '\\"', text)

        return text

    def _balance_brackets(self, text: str) -> str:
        """Balance brackets and braces in JSON."""
        # Count and balance braces
        open_braces = text.count("{")
        close_braces = text.count("}")

        if open_braces > close_braces:
            text += "}" * (open_braces - close_braces)
        elif close_braces > open_braces:
            # Remove excess closing braces from the end
            excess = close_braces - open_braces
            for _ in range(excess):
                last_brace = text.rfind("}")
                if last_brace != -1:
                    text = text[:last_brace] + text[last_brace + 1 :]

        # Count and balance brackets
        open_brackets = text.count("[")
        close_brackets = text.count("]")

        if open_brackets > close_brackets:
            text += "]" * (open_brackets - close_brackets)
        elif close_brackets > open_brackets:
            # Remove excess closing brackets from the end
            excess = close_brackets - open_brackets
            for _ in range(excess):
                last_bracket = text.rfind("]")
                if last_bracket != -1:
                    text = text[:last_bracket] + text[last_bracket + 1 :]

        return text

    def _ensure_json_wrapper(self, text: str) -> str:
        """Ensure the text is properly wrapped as JSON."""
        text = text.strip()

        if not text.startswith("{"):
            # Find the first opening brace
            match = re.search(r"\{", text)
            if match:
                text = text[match.start() :]
            else:
                # No opening brace found, wrap the content
                text = "{" + text + "}"

        if not text.endswith("}"):
            # Find the last closing brace
            last_brace = text.rfind("}")
            if last_brace != -1:
                text = text[: last_brace + 1]
            else:
                # No closing brace found, add one
                text = text + "}"

        return text

    def _extract_json_from_text(self, text: str) -> Optional[str]:
        """Extract JSON from text that might contain other content."""
        # Look for JSON-like patterns
        patterns = [
            r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}",  # Nested braces
            r"\{.*?\}",  # Simple braces
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                try:
                    # Test if this looks like valid JSON
                    json.loads(match)
                    return match
                except:
                    continue

        return None

    def _reconstruct_json_from_fragments(self, text: str) -> Optional[Dict[str, Any]]:
        """Intelligently reconstruct JSON from fragments."""
        # This is a complex strategy that tries to build valid JSON from fragments
        # For now, return a basic structure
        try:
            # Extract key-value pairs using regex
            pairs = re.findall(r'"([^"]+)"\s*:\s*([^,}]+)', text)

            if pairs:
                reconstructed = {}
                for key, value in pairs:
                    # Try to parse the value
                    value = value.strip().rstrip(",")
                    try:
                        # Try as JSON value
                        reconstructed[key] = json.loads(value)
                    except:
                        # Use as string
                        reconstructed[key] = value.strip('"')

                return reconstructed
        except:
            pass

        return None

    def _validate_json_structure(self, json_obj: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the structure of a JSON object."""
        result = {"is_valid": True, "errors": [], "warnings": []}

        # Check if it's a dictionary
        if not isinstance(json_obj, dict):
            result["is_valid"] = False
            result["errors"].append("JSON is not an object/dictionary")
            return result

        # Check for required expedition fields
        required_fields = ["date", "location"]
        for field in required_fields:
            if field not in json_obj:
                result["warnings"].append(f"Missing recommended field: {field}")

        # Check for empty values
        empty_fields = [k for k, v in json_obj.items() if v in [None, "", [], {}]]
        if empty_fields:
            result["warnings"].append(f"Empty fields found: {empty_fields}")

        return result


# Global validator instance
json_validator = JSONValidator()


def validate_and_repair_json(json_string: str, context: str = "") -> Dict[str, Any]:
    """
    Convenience function for JSON validation and repair.

    Args:
        json_string: The JSON string to validate and repair
        context: Context information for better error reporting

    Returns:
        Dictionary with validation results and repaired JSON
    """
    return json_validator.validate_and_repair(json_string, context)
