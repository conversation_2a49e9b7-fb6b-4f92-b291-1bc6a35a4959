"""
Comprehensive cleanup manager for expedition planner application.
Handles cleanup of temporary files, old sessions, and processed data.
"""

import logging
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


class ComprehensiveCleanupManager:
    """Enhanced cleanup manager with fresh start capabilities."""
    
    def __init__(self, base_dir: str = "."):
        self.base_dir = Path(base_dir)
        self.cleanup_stats = {
            "files_removed": 0,
            "directories_removed": 0,
            "space_freed_mb": 0,
            "errors": []
        }
    
    def fresh_start_cleanup(self) -> Dict[str, any]:
        """
        Perform a comprehensive cleanup for a fresh start.
        Removes all temporary files, processed outputs, and session data.
        """
        logger.info("Starting fresh start cleanup...")
        
        cleanup_targets = [
            "consolidated_outputs",
            "expedition_output_*",
            "uploads",
            "outputs",
            "temp",
            "cache",
            ".pytest_cache",
            "__pycache__",
            "*.pyc",
            "*.pyo",
            "*.log",
            "test_*.json",
            "debug_*.py",
            "extract_*.py",
            "fix_*.py",
            "run_*.py",
            "simple_*.py",
            "test_*.txt"
        ]
        
        for target in cleanup_targets:
            self._cleanup_target(target)
        
        # Clean up specific test and debug files
        self._cleanup_test_files()
        
        # Clean up any remaining temporary directories
        self._cleanup_temp_directories()
        
        logger.info(f"Fresh start cleanup completed: {self.cleanup_stats}")
        return self.cleanup_stats
    
    def _cleanup_target(self, pattern: str):
        """Clean up files/directories matching a pattern."""
        try:
            if pattern.startswith("*") or pattern.endswith("*"):
                # Handle glob patterns
                for path in self.base_dir.glob(pattern):
                    self._remove_path(path)
            else:
                # Handle exact paths
                path = self.base_dir / pattern
                if path.exists():
                    self._remove_path(path)
        except Exception as e:
            error_msg = f"Error cleaning up {pattern}: {e}"
            logger.warning(error_msg)
            self.cleanup_stats["errors"].append(error_msg)
    
    def _cleanup_test_files(self):
        """Clean up specific test and debug files."""
        test_files = [
            "analyze_test_outputs.py",
            "debug_extraction.py",
            "detailed_pattern_test.py",
            "extract_test_content.py",
            "fix_test_inconsistencies.py",
            "pattern_analysis_summary.md",
            "run_pattern_analysis.py",
            "test_configuration_changes.py",
            "test_documents.py",
            "test_extraction_*.json",
            "test_final_fix.py",
            "test_json_fixes.py",
            "test_json_generator.py",
            "test_json_generator_fixed.py",
            "test_langchain_improvements.py",
            "test_modernized_pipeline.py",
            "test_ocr_*.txt",
            "test_ocr_improvements.py",
            "test_pattern_analysis.py",
            "test_pattern_analyzer.py",
            "test_template_tool.py",
            "test_two_phase_approach.py"
        ]
        
        for file_pattern in test_files:
            for file_path in self.base_dir.glob(file_pattern):
                self._remove_path(file_path)
    
    def _cleanup_temp_directories(self):
        """Clean up temporary directories that might have been created."""
        temp_patterns = [
            "tmp*",
            "temp*",
            ".tmp*",
            "session_*",
            "upload_*",
            "processing_*"
        ]
        
        for pattern in temp_patterns:
            for path in self.base_dir.glob(pattern):
                if path.is_dir():
                    self._remove_path(path)
    
    def _remove_path(self, path: Path):
        """Safely remove a file or directory."""
        try:
            if not path.exists():
                return
            
            # Calculate size before removal
            size_mb = self._get_size_mb(path)
            
            if path.is_file():
                path.unlink()
                self.cleanup_stats["files_removed"] += 1
                logger.debug(f"Removed file: {path}")
            elif path.is_dir():
                shutil.rmtree(path, ignore_errors=True)
                self.cleanup_stats["directories_removed"] += 1
                logger.debug(f"Removed directory: {path}")
            
            self.cleanup_stats["space_freed_mb"] += size_mb
            
        except Exception as e:
            error_msg = f"Error removing {path}: {e}"
            logger.warning(error_msg)
            self.cleanup_stats["errors"].append(error_msg)
    
    def _get_size_mb(self, path: Path) -> float:
        """Get size of file or directory in MB."""
        try:
            if path.is_file():
                return path.stat().st_size / (1024 * 1024)
            elif path.is_dir():
                total_size = 0
                for file_path in path.rglob("*"):
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
                return total_size / (1024 * 1024)
        except Exception:
            return 0.0
        return 0.0
    
    def cleanup_old_sessions(self, hours_old: int = 24) -> Dict[str, any]:
        """Clean up old session data."""
        cutoff_time = datetime.now() - timedelta(hours=hours_old)
        session_stats = {"removed": 0, "errors": []}
        
        # Look for session directories
        for session_dir in self.base_dir.glob("**/session_*"):
            try:
                if session_dir.is_dir():
                    # Check modification time
                    mod_time = datetime.fromtimestamp(session_dir.stat().st_mtime)
                    if mod_time < cutoff_time:
                        shutil.rmtree(session_dir, ignore_errors=True)
                        session_stats["removed"] += 1
                        logger.info(f"Removed old session: {session_dir}")
            except Exception as e:
                error_msg = f"Error cleaning session {session_dir}: {e}"
                logger.warning(error_msg)
                session_stats["errors"].append(error_msg)
        
        return session_stats
    
    def get_disk_usage(self) -> Dict[str, float]:
        """Get current disk usage statistics."""
        usage = {}
        
        directories_to_check = [
            "consolidated_outputs",
            "uploads",
            "outputs",
            "legacy",
            "tests"
        ]
        
        for dir_name in directories_to_check:
            dir_path = self.base_dir / dir_name
            if dir_path.exists():
                usage[dir_name] = self._get_size_mb(dir_path)
        
        return usage


def perform_fresh_start_cleanup(base_dir: str = ".") -> Dict[str, any]:
    """
    Convenience function to perform a complete fresh start cleanup.
    """
    cleanup_manager = ComprehensiveCleanupManager(base_dir)
    return cleanup_manager.fresh_start_cleanup()


if __name__ == "__main__":
    # Allow running as script for manual cleanup
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--fresh-start":
        print("Performing fresh start cleanup...")
        stats = perform_fresh_start_cleanup()
        print(f"Cleanup completed:")
        print(f"  Files removed: {stats['files_removed']}")
        print(f"  Directories removed: {stats['directories_removed']}")
        print(f"  Space freed: {stats['space_freed_mb']:.2f} MB")
        if stats['errors']:
            print(f"  Errors: {len(stats['errors'])}")
            for error in stats['errors']:
                print(f"    - {error}")
    else:
        print("Usage: python cleanup_manager.py --fresh-start")
