"""
File cleanup utilities for expedition planner.
"""

import logging
import os
import shutil
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


class FileCleanupManager:
    """Manages file cleanup operations for the expedition planner."""

    def __init__(self, base_upload_dir: str, base_output_dir: str):
        """Initialize the file cleanup manager."""
        self.upload_dir = Path(base_upload_dir)
        self.output_dir = Path(base_output_dir)
        self.cleanup_stats = {
            "files_removed": 0,
            "directories_removed": 0,
            "space_freed_mb": 0,
            "errors": [],
        }

    def cleanup_old_uploads(self, days_old: int = 7) -> Dict:
        """
        Clean up upload files older than specified days.

        Args:
            days_old: Number of days after which files should be cleaned up

        Returns:
            Dictionary with cleanup statistics
        """
        logger.info(f"Starting cleanup of uploads older than {days_old} days")

        cutoff_time = time.time() - (days_old * 24 * 60 * 60)

        try:
            if not self.upload_dir.exists():
                logger.warning(f"Upload directory {self.upload_dir} does not exist")
                return self.cleanup_stats

            # Clean up old session directories
            for item in self.upload_dir.iterdir():
                if item.is_dir():
                    # Check if directory name contains timestamp
                    if self._is_session_directory(item.name):
                        if item.stat().st_mtime < cutoff_time:
                            self._remove_directory_safely(item)
                elif item.is_file():
                    # Clean up individual files
                    if item.stat().st_mtime < cutoff_time:
                        self._remove_file_safely(item)

        except Exception as e:
            error_msg = f"Error during upload cleanup: {e}"
            logger.error(error_msg)
            self.cleanup_stats["errors"].append(error_msg)

        return self.cleanup_stats

    def cleanup_duplicate_files(self) -> Dict:
        """Remove duplicate files based on content hash."""
        logger.info("Starting duplicate file cleanup")

        try:
            file_hashes = {}
            duplicates = []

            # Scan all files and compute hashes
            for file_path in self.upload_dir.rglob("*"):
                if file_path.is_file():
                    try:
                        file_hash = self._compute_file_hash(file_path)
                        if file_hash in file_hashes:
                            duplicates.append(file_path)
                        else:
                            file_hashes[file_hash] = file_path
                    except Exception as e:
                        logger.warning(f"Could not hash file {file_path}: {e}")

            # Remove duplicates
            for duplicate in duplicates:
                self._remove_file_safely(duplicate)

        except Exception as e:
            error_msg = f"Error during duplicate cleanup: {e}"
            logger.error(error_msg)
            self.cleanup_stats["errors"].append(error_msg)

        return self.cleanup_stats

    def cleanup_empty_directories(self) -> Dict:
        """Remove empty directories."""
        logger.info("Cleaning up empty directories")

        try:
            # Walk directories bottom-up to handle nested empty dirs
            for root, dirs, files in os.walk(self.upload_dir, topdown=False):
                for dir_name in dirs:
                    dir_path = Path(root) / dir_name
                    if dir_path.exists() and not any(dir_path.iterdir()):
                        self._remove_directory_safely(dir_path)

        except Exception as e:
            error_msg = f"Error during empty directory cleanup: {e}"
            logger.error(error_msg)
            self.cleanup_stats["errors"].append(error_msg)

        return self.cleanup_stats

    def enforce_size_limits(self, max_size_mb: int = 100) -> Dict:
        """Enforce size limits on upload directory."""
        logger.info(f"Enforcing size limit of {max_size_mb}MB")

        try:
            total_size = self._get_directory_size(self.upload_dir)
            max_size_bytes = max_size_mb * 1024 * 1024

            if total_size > max_size_bytes:
                # Remove oldest files until under limit
                files_by_age = []
                for file_path in self.upload_dir.rglob("*"):
                    if file_path.is_file():
                        files_by_age.append((file_path.stat().st_mtime, file_path))

                files_by_age.sort()  # Oldest first

                current_size = total_size
                for _, file_path in files_by_age:
                    if current_size <= max_size_bytes:
                        break
                    file_size = file_path.stat().st_size
                    self._remove_file_safely(file_path)
                    current_size -= file_size

        except Exception as e:
            error_msg = f"Error during size limit enforcement: {e}"
            logger.error(error_msg)
            self.cleanup_stats["errors"].append(error_msg)

        return self.cleanup_stats

    def _is_session_directory(self, dir_name: str) -> bool:
        """Check if directory name follows session naming pattern."""
        # Pattern: UUID_timestamp or timestamp_filename
        return "_" in dir_name and (
            len(dir_name.split("_")[0]) > 8  # UUID-like
            or dir_name.split("_")[1].isdigit()  # timestamp
        )

    def _remove_file_safely(self, file_path: Path) -> None:
        """Safely remove a file with error handling."""
        try:
            file_size = file_path.stat().st_size
            file_path.unlink()
            self.cleanup_stats["files_removed"] += 1
            self.cleanup_stats["space_freed_mb"] += file_size / (1024 * 1024)
            logger.debug(f"Removed file: {file_path}")
        except Exception as e:
            error_msg = f"Failed to remove file {file_path}: {e}"
            logger.warning(error_msg)
            self.cleanup_stats["errors"].append(error_msg)

    def _remove_directory_safely(self, dir_path: Path) -> None:
        """Safely remove a directory with error handling."""
        try:
            dir_size = self._get_directory_size(dir_path)
            shutil.rmtree(dir_path)
            self.cleanup_stats["directories_removed"] += 1
            self.cleanup_stats["space_freed_mb"] += dir_size / (1024 * 1024)
            logger.debug(f"Removed directory: {dir_path}")
        except Exception as e:
            error_msg = f"Failed to remove directory {dir_path}: {e}"
            logger.warning(error_msg)
            self.cleanup_stats["errors"].append(error_msg)

    def _compute_file_hash(self, file_path: Path) -> str:
        """Compute SHA256 hash of file content."""
        import hashlib

        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()

    def _get_directory_size(self, dir_path: Path) -> int:
        """Get total size of directory in bytes."""
        total_size = 0
        try:
            for file_path in dir_path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception as e:
            logger.warning(f"Error calculating directory size for {dir_path}: {e}")
        return total_size

    def get_cleanup_report(self) -> str:
        """Generate a human-readable cleanup report."""
        report = f"""
File Cleanup Report:
==================
Files removed: {self.cleanup_stats["files_removed"]}
Directories removed: {self.cleanup_stats["directories_removed"]}
Space freed: {self.cleanup_stats["space_freed_mb"]:.2f} MB
Errors encountered: {len(self.cleanup_stats["errors"])}
"""
        if self.cleanup_stats["errors"]:
            report += "\nErrors:\n"
            for error in self.cleanup_stats["errors"]:
                report += f"  - {error}\n"

        return report


def run_cleanup(
    upload_dir: str, output_dir: str, days_old: int = 7, max_size_mb: int = 100
) -> Dict:
    """
    Run complete cleanup operation.

    Args:
        upload_dir: Upload directory path
        output_dir: Output directory path
        days_old: Days after which files are considered old
        max_size_mb: Maximum size limit in MB

    Returns:
        Cleanup statistics dictionary
    """
    cleanup_manager = FileCleanupManager(upload_dir, output_dir)

    # Run all cleanup operations
    cleanup_manager.cleanup_old_uploads(days_old)
    cleanup_manager.cleanup_duplicate_files()
    cleanup_manager.cleanup_empty_directories()
    cleanup_manager.enforce_size_limits(max_size_mb)

    logger.info(cleanup_manager.get_cleanup_report())

    return cleanup_manager.cleanup_stats
