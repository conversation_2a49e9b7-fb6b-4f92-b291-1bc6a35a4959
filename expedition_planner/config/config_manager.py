"""
Centralized configuration management for expedition planner.
"""

import json
import logging
import os
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


@dataclass
class OllamaConfig:
    """Ollama LLM configuration."""

    base_url: str = "http://127.0.0.1:11434"
    model: str = "mistral-7b-v0-1-gguf:latest"
    temperature: float = 0.1
    timeout: int = 60
    num_ctx: int = 4096
    num_predict: int = 1024
    repeat_penalty: float = 1.1
    top_k: int = 40
    top_p: float = 0.9
    request_timeout: int = 60
    keep_alive: str = "5m"


@dataclass
class AgentConfig:
    """LangChain agent configuration."""

    max_iterations: int = 3
    max_execution_time: int = 180
    verbose: bool = True
    return_intermediate_steps: bool = True
    early_stopping_method: str = "force"
    handle_parsing_errors: bool = True
    trim_intermediate_steps: int = 10


@dataclass
class FileConfig:
    """File processing configuration."""

    supported_extensions: List[str] = field(
        default_factory=lambda: [".pdf", ".docx", ".doc", ".txt", ".md"]
    )
    max_file_size_mb: int = 50
    batch_size: int = 5
    output_formats: List[str] = field(default_factory=lambda: ["json"])
    cleanup_days: int = 7
    max_storage_mb: int = 100


@dataclass
class WebConfig:
    """Web interface configuration."""

    host: str = "localhost"
    port: int = 8080
    debug: bool = False
    secret_key: str = "dev-key-change-in-production"
    max_content_length: int = 100 * 1024 * 1024  # 100MB
    upload_timeout: int = 300  # 5 minutes


@dataclass
class PerformanceConfig:
    """Performance optimization configuration."""

    max_workers: int = 4
    max_memory_percent: float = 80.0
    max_memory_mb: float = 500.0
    parallel_chunk_size: int = 2
    llm_context_limit: int = 4096
    cache_size_limit: int = 100


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration."""

    llm_failure_threshold: int = 3
    llm_recovery_timeout: int = 30
    file_failure_threshold: int = 5
    file_recovery_timeout: int = 60


@dataclass
class LoggingConfig:
    """Logging configuration."""

    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size_mb: int = 10
    backup_count: int = 5


@dataclass
class ExpeditionPlannerConfig:
    """Main configuration class."""

    ollama: OllamaConfig = field(default_factory=OllamaConfig)
    agents: AgentConfig = field(default_factory=AgentConfig)
    files: FileConfig = field(default_factory=FileConfig)
    web: WebConfig = field(default_factory=WebConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    circuit_breakers: CircuitBreakerConfig = field(default_factory=CircuitBreakerConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)

    # Directory paths
    base_dir: Path = field(default_factory=lambda: Path(__file__).parent.parent)
    uploads_dir: Path = field(
        default_factory=lambda: Path(__file__).parent.parent / "uploads"
    )
    outputs_dir: Path = field(
        default_factory=lambda: Path(__file__).parent.parent.parent
        / "consolidated_outputs"
    )
    templates_dir: Path = field(
        default_factory=lambda: Path(__file__).parent.parent / "templates"
    )

    def __post_init__(self):
        """Post-initialization setup."""
        # Ensure directories exist
        self.uploads_dir.mkdir(exist_ok=True)
        self.outputs_dir.mkdir(exist_ok=True)
        self.templates_dir.mkdir(exist_ok=True)

        # Apply environment variable overrides
        self._apply_env_overrides()

    def _apply_env_overrides(self):
        """Apply environment variable overrides."""
        # Ollama overrides
        if os.getenv("OLLAMA_BASE_URL"):
            self.ollama.base_url = os.getenv("OLLAMA_BASE_URL")
        if os.getenv("OLLAMA_MODEL"):
            self.ollama.model = os.getenv("OLLAMA_MODEL")
        if os.getenv("OLLAMA_TEMPERATURE"):
            self.ollama.temperature = float(os.getenv("OLLAMA_TEMPERATURE"))

        # Web overrides
        if os.getenv("WEB_HOST"):
            self.web.host = os.getenv("WEB_HOST")
        if os.getenv("WEB_PORT"):
            self.web.port = int(os.getenv("WEB_PORT"))
        if os.getenv("WEB_DEBUG"):
            self.web.debug = os.getenv("WEB_DEBUG").lower() == "true"
        if os.getenv("SECRET_KEY"):
            self.web.secret_key = os.getenv("SECRET_KEY")

        # File processing overrides
        if os.getenv("MAX_FILE_SIZE_MB"):
            self.files.max_file_size_mb = int(os.getenv("MAX_FILE_SIZE_MB"))
        if os.getenv("CLEANUP_DAYS"):
            self.files.cleanup_days = int(os.getenv("CLEANUP_DAYS"))

        # Logging overrides
        if os.getenv("LOG_LEVEL"):
            self.logging.level = os.getenv("LOG_LEVEL")
        if os.getenv("LOG_FILE"):
            self.logging.file_path = os.getenv("LOG_FILE")

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "ollama": {
                "base_url": self.ollama.base_url,
                "model": self.ollama.model,
                "temperature": self.ollama.temperature,
                "timeout": self.ollama.timeout,
                "num_ctx": self.ollama.num_ctx,
                "num_predict": self.ollama.num_predict,
                "repeat_penalty": self.ollama.repeat_penalty,
                "top_k": self.ollama.top_k,
                "top_p": self.ollama.top_p,
                "request_timeout": self.ollama.request_timeout,
                "keep_alive": self.ollama.keep_alive,
            },
            "agents": {
                "max_iterations": self.agents.max_iterations,
                "max_execution_time": self.agents.max_execution_time,
                "verbose": self.agents.verbose,
                "return_intermediate_steps": self.agents.return_intermediate_steps,
                "early_stopping_method": self.agents.early_stopping_method,
                "handle_parsing_errors": self.agents.handle_parsing_errors,
                "trim_intermediate_steps": self.agents.trim_intermediate_steps,
            },
            "files": {
                "supported_extensions": self.files.supported_extensions,
                "max_file_size_mb": self.files.max_file_size_mb,
                "batch_size": self.files.batch_size,
                "output_formats": self.files.output_formats,
                "cleanup_days": self.files.cleanup_days,
                "max_storage_mb": self.files.max_storage_mb,
            },
            "web": {
                "host": self.web.host,
                "port": self.web.port,
                "debug": self.web.debug,
                "max_content_length": self.web.max_content_length,
                "upload_timeout": self.web.upload_timeout,
            },
            "performance": {
                "max_workers": self.performance.max_workers,
                "max_memory_percent": self.performance.max_memory_percent,
                "max_memory_mb": self.performance.max_memory_mb,
                "parallel_chunk_size": self.performance.parallel_chunk_size,
                "llm_context_limit": self.performance.llm_context_limit,
                "cache_size_limit": self.performance.cache_size_limit,
            },
            "circuit_breakers": {
                "llm_failure_threshold": self.circuit_breakers.llm_failure_threshold,
                "llm_recovery_timeout": self.circuit_breakers.llm_recovery_timeout,
                "file_failure_threshold": self.circuit_breakers.file_failure_threshold,
                "file_recovery_timeout": self.circuit_breakers.file_recovery_timeout,
            },
            "logging": {
                "level": self.logging.level,
                "format": self.logging.format,
                "file_path": self.logging.file_path,
                "max_file_size_mb": self.logging.max_file_size_mb,
                "backup_count": self.logging.backup_count,
            },
            "directories": {
                "base": str(self.base_dir),
                "uploads": str(self.uploads_dir),
                "outputs": str(self.outputs_dir),
                "templates": str(self.templates_dir),
            },
        }

    def save_to_file(self, file_path: Union[str, Path]) -> None:
        """Save configuration to JSON file."""
        config_dict = self.to_dict()
        with open(file_path, "w") as f:
            json.dump(config_dict, f, indent=2)
        logger.info(f"Configuration saved to {file_path}")

    @classmethod
    def load_from_file(cls, file_path: Union[str, Path]) -> "ExpeditionPlannerConfig":
        """Load configuration from JSON file."""
        with open(file_path) as f:
            config_dict = json.load(f)

        # Create config instance with loaded values
        config = cls()

        # Update with loaded values
        if "ollama" in config_dict:
            for key, value in config_dict["ollama"].items():
                if hasattr(config.ollama, key):
                    setattr(config.ollama, key, value)

        if "agents" in config_dict:
            for key, value in config_dict["agents"].items():
                if hasattr(config.agents, key):
                    setattr(config.agents, key, value)

        if "files" in config_dict:
            for key, value in config_dict["files"].items():
                if hasattr(config.files, key):
                    setattr(config.files, key, value)

        if "web" in config_dict:
            for key, value in config_dict["web"].items():
                if hasattr(config.web, key):
                    setattr(config.web, key, value)

        if "performance" in config_dict:
            for key, value in config_dict["performance"].items():
                if hasattr(config.performance, key):
                    setattr(config.performance, key, value)

        if "circuit_breakers" in config_dict:
            for key, value in config_dict["circuit_breakers"].items():
                if hasattr(config.circuit_breakers, key):
                    setattr(config.circuit_breakers, key, value)

        if "logging" in config_dict:
            for key, value in config_dict["logging"].items():
                if hasattr(config.logging, key):
                    setattr(config.logging, key, value)

        logger.info(f"Configuration loaded from {file_path}")
        return config


class ConfigManager:
    """Manages configuration loading and validation."""

    def __init__(self):
        """Initialize configuration manager."""
        self._config: Optional[ExpeditionPlannerConfig] = None
        self._config_file: Optional[Path] = None

    def load_config(
        self, config_file: Optional[Union[str, Path]] = None
    ) -> ExpeditionPlannerConfig:
        """
        Load configuration from file or create default.

        Args:
            config_file: Optional path to configuration file

        Returns:
            Loaded configuration
        """
        if config_file:
            config_path = Path(config_file)
            if config_path.exists():
                self._config = ExpeditionPlannerConfig.load_from_file(config_path)
                self._config_file = config_path
            else:
                logger.warning(f"Config file {config_path} not found, using defaults")
                self._config = ExpeditionPlannerConfig()
        else:
            # Look for config file in standard locations
            possible_paths = [
                Path("expedition_config.json"),
                Path.home() / ".expedition_planner" / "config.json",
                Path("/etc/expedition_planner/config.json"),
            ]

            for path in possible_paths:
                if path.exists():
                    self._config = ExpeditionPlannerConfig.load_from_file(path)
                    self._config_file = path
                    break
            else:
                # No config file found, use defaults
                self._config = ExpeditionPlannerConfig()

        return self._config

    def get_config(self) -> ExpeditionPlannerConfig:
        """Get current configuration."""
        if self._config is None:
            self._config = self.load_config()
        return self._config

    def save_config(self, config_file: Optional[Union[str, Path]] = None) -> None:
        """Save current configuration to file."""
        if self._config is None:
            raise ValueError("No configuration loaded")

        save_path = config_file or self._config_file or Path("expedition_config.json")
        self._config.save_to_file(save_path)
        self._config_file = Path(save_path)

    def validate_config(self) -> Dict[str, Any]:
        """Validate current configuration."""
        if self._config is None:
            return {"valid": False, "errors": ["No configuration loaded"]}

        errors = []
        warnings = []

        # Validate Ollama configuration
        try:
            import requests

            response = requests.get(
                f"{self._config.ollama.base_url}/api/tags", timeout=5
            )
            if response.status_code != 200:
                errors.append("Ollama service not accessible")
        except Exception as e:
            warnings.append(f"Could not verify Ollama connection: {e}")

        # Validate directories
        for dir_name, dir_path in [
            ("uploads", self._config.uploads_dir),
            ("outputs", self._config.outputs_dir),
            ("templates", self._config.templates_dir),
        ]:
            if not dir_path.exists():
                try:
                    dir_path.mkdir(parents=True, exist_ok=True)
                    warnings.append(f"Created missing {dir_name} directory: {dir_path}")
                except Exception as e:
                    errors.append(f"Cannot create {dir_name} directory {dir_path}: {e}")

        # Validate file size limits
        if self._config.files.max_file_size_mb <= 0:
            errors.append("Max file size must be positive")

        if self._config.files.max_file_size_mb > 1000:
            warnings.append("Max file size is very large (>1GB)")

        # Validate web configuration
        if not (1 <= self._config.web.port <= 65535):
            errors.append("Web port must be between 1 and 65535")

        # Validate performance settings
        if self._config.performance.max_workers <= 0:
            errors.append("Max workers must be positive")

        if (
            self._config.performance.max_memory_percent <= 0
            or self._config.performance.max_memory_percent > 100
        ):
            errors.append("Max memory percent must be between 0 and 100")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "config_file": str(self._config_file) if self._config_file else None,
        }


# Global configuration manager instance
config_manager = ConfigManager()


def get_config() -> ExpeditionPlannerConfig:
    """Get the global configuration instance."""
    return config_manager.get_config()


def validate_config() -> Dict[str, Any]:
    """Validate the global configuration."""
    return config_manager.validate_config()
