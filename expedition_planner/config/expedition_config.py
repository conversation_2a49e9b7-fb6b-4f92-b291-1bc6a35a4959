"""
Configuration management system for Expedition Planner.
Provides dataclass-based configuration with environment variable support.
"""

import os
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Tuple


@dataclass
class LLMConfig:
    """Configuration for LLM settings."""

    model: str = "mistral-7b-v0-1-gguf:latest"
    base_url: str = "http://127.0.0.1:11434"
    temperature: float = 0.2
    num_ctx: int = 4096
    num_predict: int = 1024
    repeat_penalty: float = 1.1
    top_k: int = 40
    top_p: float = 0.9
    max_retries: int = 3
    timeout: int = 120
    request_timeout: int = 60
    keep_alive: str = "5m"


@dataclass
class ProcessingConfig:
    """Configuration for document processing."""

    max_iterations: int = 2
    max_execution_time: int = 300
    enable_verbose_logging: bool = False
    batch_size: int = 10
    cache_ttl_seconds: int = 3600
    text_chunk_size: int = 6000
    max_file_size_mb: int = 50
    concurrent_workers: int = 4


@dataclass
class SecurityConfig:
    """Configuration for security settings."""

    max_file_size_mb: int = 50
    allowed_extensions: Tuple[str, ...] = (".pdf", ".docx", ".doc", ".txt", ".md")
    max_sessions: int = 100
    session_ttl_hours: int = 24
    upload_sandbox_dir: str = "uploads"
    enable_file_validation: bool = True
    max_upload_rate_per_minute: int = 10


@dataclass
class ValidationConfig:
    """Configuration for data validation."""

    required_fields: Tuple[str, ...] = ("date", "location", "activities")
    time_format_regex: str = r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
    date_format_regex: str = r"^\d{4}-\d{2}-\d{2}$"
    tide_height_range: Tuple[float, float] = (0.0, 15.0)
    duration_range_minutes: Tuple[int, int] = (30, 720)
    max_groups_per_operation: int = 10
    max_schedule_events: int = 50


@dataclass
class OutputConfig:
    """Configuration for output generation."""

    default_output_format: str = "json"
    supported_formats: Tuple[str, ...] = ("json", "pdf", "excel")
    output_directory: str = "outputs"
    filename_pattern: str = "{location}-{date}.json"
    include_metadata: bool = True
    pretty_print_json: bool = True
    backup_outputs: bool = True


@dataclass
class ExpeditionConfig:
    """Main configuration container for Expedition Planner."""

    llm: LLMConfig
    processing: ProcessingConfig
    security: SecurityConfig
    validation: ValidationConfig
    output: OutputConfig

    @classmethod
    def load_from_env(cls) -> "ExpeditionConfig":
        """Load configuration from environment variables with sensible defaults."""
        return cls(
            llm=LLMConfig(
                model=os.getenv("EXPEDITION_LLM_MODEL", "mistral-7b-v0-1-gguf:latest"),
                base_url=os.getenv("EXPEDITION_LLM_BASE_URL", "http://127.0.0.1:11434"),
                temperature=float(os.getenv("EXPEDITION_LLM_TEMPERATURE", "0.2")),
                num_ctx=int(os.getenv("EXPEDITION_LLM_NUM_CTX", "4096")),
                num_predict=int(os.getenv("EXPEDITION_LLM_NUM_PREDICT", "1024")),
                max_retries=int(os.getenv("EXPEDITION_LLM_MAX_RETRIES", "3")),
                timeout=int(os.getenv("EXPEDITION_LLM_TIMEOUT", "120")),
            ),
            processing=ProcessingConfig(
                max_iterations=int(os.getenv("EXPEDITION_MAX_ITERATIONS", "2")),
                max_execution_time=int(
                    os.getenv("EXPEDITION_MAX_EXECUTION_TIME", "300")
                ),
                enable_verbose_logging=os.getenv(
                    "EXPEDITION_VERBOSE_LOGGING", "false"
                ).lower()
                == "true",
                batch_size=int(os.getenv("EXPEDITION_BATCH_SIZE", "10")),
                cache_ttl_seconds=int(os.getenv("EXPEDITION_CACHE_TTL", "3600")),
                text_chunk_size=int(os.getenv("EXPEDITION_TEXT_CHUNK_SIZE", "6000")),
                max_file_size_mb=int(os.getenv("EXPEDITION_MAX_FILE_SIZE_MB", "50")),
                concurrent_workers=int(os.getenv("EXPEDITION_CONCURRENT_WORKERS", "4")),
            ),
            security=SecurityConfig(
                max_file_size_mb=int(
                    os.getenv("EXPEDITION_SECURITY_MAX_FILE_SIZE_MB", "50")
                ),
                max_sessions=int(os.getenv("EXPEDITION_SECURITY_MAX_SESSIONS", "100")),
                session_ttl_hours=int(
                    os.getenv("EXPEDITION_SECURITY_SESSION_TTL_HOURS", "24")
                ),
                upload_sandbox_dir=os.getenv(
                    "EXPEDITION_SECURITY_UPLOAD_DIR", "uploads"
                ),
                enable_file_validation=os.getenv(
                    "EXPEDITION_SECURITY_ENABLE_VALIDATION", "true"
                ).lower()
                == "true",
                max_upload_rate_per_minute=int(
                    os.getenv("EXPEDITION_SECURITY_MAX_UPLOAD_RATE", "10")
                ),
            ),
            validation=ValidationConfig(
                tide_height_range=(
                    float(os.getenv("EXPEDITION_VALIDATION_TIDE_MIN", "0.0")),
                    float(os.getenv("EXPEDITION_VALIDATION_TIDE_MAX", "15.0")),
                ),
                duration_range_minutes=(
                    int(os.getenv("EXPEDITION_VALIDATION_DURATION_MIN", "30")),
                    int(os.getenv("EXPEDITION_VALIDATION_DURATION_MAX", "720")),
                ),
                max_groups_per_operation=int(
                    os.getenv("EXPEDITION_VALIDATION_MAX_GROUPS", "10")
                ),
                max_schedule_events=int(
                    os.getenv("EXPEDITION_VALIDATION_MAX_EVENTS", "50")
                ),
            ),
            output=OutputConfig(
                default_output_format=os.getenv("EXPEDITION_OUTPUT_FORMAT", "json"),
                output_directory=os.getenv("EXPEDITION_OUTPUT_DIR", "outputs"),
                filename_pattern=os.getenv(
                    "EXPEDITION_OUTPUT_FILENAME_PATTERN", "{location}-{date}.json"
                ),
                include_metadata=os.getenv(
                    "EXPEDITION_OUTPUT_INCLUDE_METADATA", "true"
                ).lower()
                == "true",
                pretty_print_json=os.getenv(
                    "EXPEDITION_OUTPUT_PRETTY_PRINT", "true"
                ).lower()
                == "true",
                backup_outputs=os.getenv("EXPEDITION_OUTPUT_BACKUP", "true").lower()
                == "true",
            ),
        )

    @classmethod
    def load_from_file(cls, config_path: str) -> "ExpeditionConfig":
        """Load configuration from a JSON or YAML file."""
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        # For now, just return environment-based config
        # TODO: Implement file-based configuration loading
        return cls.load_from_env()

    def validate(self) -> bool:
        """Validate configuration settings."""
        errors = []

        # Validate LLM config
        if self.llm.temperature < 0 or self.llm.temperature > 2:
            errors.append("LLM temperature must be between 0 and 2")

        if self.llm.num_ctx < 512:
            errors.append("LLM context size must be at least 512")

        # Validate processing config
        if self.processing.max_iterations < 1:
            errors.append("Max iterations must be at least 1")

        if self.processing.batch_size < 1:
            errors.append("Batch size must be at least 1")

        # Validate security config
        if self.security.max_file_size_mb < 1:
            errors.append("Max file size must be at least 1MB")

        # Validate validation config
        if self.validation.tide_height_range[0] >= self.validation.tide_height_range[1]:
            errors.append("Tide height range minimum must be less than maximum")

        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")

        return True

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "llm": self.llm.__dict__,
            "processing": self.processing.__dict__,
            "security": self.security.__dict__,
            "validation": self.validation.__dict__,
            "output": self.output.__dict__,
        }


def get_config() -> ExpeditionConfig:
    """Get the global configuration instance."""
    return ExpeditionConfig.load_from_env()


def validate_environment() -> bool:
    """Validate that the environment is properly configured."""
    try:
        config = get_config()
        config.validate()
        return True
    except Exception as e:
        print(f"Environment validation failed: {e}")
        return False
