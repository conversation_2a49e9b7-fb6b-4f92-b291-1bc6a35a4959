"""
Centralized configuration management for expedition planner.
Handles environment variables, validation, and configuration loading.
"""

import os
import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """Database configuration."""
    url: str = "sqlite:///expedition_planner.db"
    echo: bool = False
    pool_size: int = 5
    max_overflow: int = 10


@dataclass
class DirectoryConfig:
    """Directory configuration."""
    uploads: str = "uploads"
    outputs: str = "outputs"
    temp: str = "temp"
    logs: str = "logs"
    
    def __post_init__(self):
        """Ensure directories exist."""
        for dir_path in [self.uploads, self.outputs, self.temp, self.logs]:
            Path(dir_path).mkdir(parents=True, exist_ok=True)


@dataclass
class SessionConfig:
    """Session management configuration."""
    timeout_hours: int = 24
    max_active_sessions: int = 100
    cleanup_interval_minutes: int = 60


@dataclass
class FileConfig:
    """File handling configuration."""
    max_file_size_mb: int = 50
    max_files_per_upload: int = 10
    allowed_extensions: List[str] = field(default_factory=lambda: [
        '.pdf', '.docx', '.doc', '.txt', '.md', 
        '.png', '.jpg', '.jpeg', '.tiff', '.bmp'
    ])


@dataclass
class LLMConfig:
    """LLM configuration."""
    provider: str = "ollama"
    model: str = "mistral-7b-v0-1-gguf:latest"
    base_url: str = "http://localhost:11434"
    timeout_seconds: int = 300
    max_retries: int = 3
    temperature: float = 0.1


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size_mb: int = 10
    backup_count: int = 5


@dataclass
class SecurityConfig:
    """Security configuration."""
    secret_key: str = "dev-key-change-in-production"
    allowed_hosts: List[str] = field(default_factory=lambda: ["localhost", "127.0.0.1"])
    cors_origins: List[str] = field(default_factory=lambda: ["*"])
    rate_limit_per_minute: int = 60


@dataclass
class AppConfig:
    """Main application configuration."""
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8080
    
    # Sub-configurations
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    directories: DirectoryConfig = field(default_factory=DirectoryConfig)
    sessions: SessionConfig = field(default_factory=SessionConfig)
    files: FileConfig = field(default_factory=FileConfig)
    llm: LLMConfig = field(default_factory=LLMConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    
    @classmethod
    def from_env(cls) -> 'AppConfig':
        """Create configuration from environment variables."""
        config = cls()
        
        # Main app config
        config.debug = _get_bool_env("DEBUG", config.debug)
        config.host = os.getenv("HOST", config.host)
        config.port = _get_int_env("PORT", config.port)
        
        # Database config
        config.database.url = os.getenv("DATABASE_URL", config.database.url)
        config.database.echo = _get_bool_env("DATABASE_ECHO", config.database.echo)
        config.database.pool_size = _get_int_env("DATABASE_POOL_SIZE", config.database.pool_size)
        
        # Directory config
        config.directories.uploads = os.getenv("UPLOADS_DIR", config.directories.uploads)
        config.directories.outputs = os.getenv("OUTPUTS_DIR", config.directories.outputs)
        config.directories.temp = os.getenv("TEMP_DIR", config.directories.temp)
        config.directories.logs = os.getenv("LOGS_DIR", config.directories.logs)
        
        # Session config
        config.sessions.timeout_hours = _get_int_env("SESSION_TIMEOUT_HOURS", config.sessions.timeout_hours)
        config.sessions.max_active_sessions = _get_int_env("MAX_ACTIVE_SESSIONS", config.sessions.max_active_sessions)
        
        # File config
        config.files.max_file_size_mb = _get_int_env("MAX_FILE_SIZE_MB", config.files.max_file_size_mb)
        config.files.max_files_per_upload = _get_int_env("MAX_FILES_PER_UPLOAD", config.files.max_files_per_upload)
        
        # LLM config
        config.llm.provider = os.getenv("LLM_PROVIDER", config.llm.provider)
        config.llm.model = os.getenv("LLM_MODEL", config.llm.model)
        config.llm.base_url = os.getenv("LLM_BASE_URL", config.llm.base_url)
        config.llm.timeout_seconds = _get_int_env("LLM_TIMEOUT_SECONDS", config.llm.timeout_seconds)
        config.llm.temperature = _get_float_env("LLM_TEMPERATURE", config.llm.temperature)
        
        # Logging config
        config.logging.level = os.getenv("LOG_LEVEL", config.logging.level)
        config.logging.file_path = os.getenv("LOG_FILE", config.logging.file_path)
        config.logging.max_file_size_mb = _get_int_env("LOG_MAX_FILE_SIZE_MB", config.logging.max_file_size_mb)
        
        # Security config
        config.security.secret_key = os.getenv("SECRET_KEY", config.security.secret_key)
        config.security.rate_limit_per_minute = _get_int_env("RATE_LIMIT_PER_MINUTE", config.security.rate_limit_per_minute)
        
        # Parse list environment variables
        if os.getenv("ALLOWED_HOSTS"):
            config.security.allowed_hosts = os.getenv("ALLOWED_HOSTS").split(",")
        
        if os.getenv("CORS_ORIGINS"):
            config.security.cors_origins = os.getenv("CORS_ORIGINS").split(",")
        
        # Ensure directories exist after loading config
        config.directories.__post_init__()
        
        return config
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Validate port
        if not (1 <= self.port <= 65535):
            errors.append(f"Invalid port: {self.port}")
        
        # Validate file size limits
        if self.files.max_file_size_mb <= 0:
            errors.append(f"Invalid max file size: {self.files.max_file_size_mb}")
        
        if self.files.max_files_per_upload <= 0:
            errors.append(f"Invalid max files per upload: {self.files.max_files_per_upload}")
        
        # Validate session config
        if self.sessions.timeout_hours <= 0:
            errors.append(f"Invalid session timeout: {self.sessions.timeout_hours}")
        
        if self.sessions.max_active_sessions <= 0:
            errors.append(f"Invalid max active sessions: {self.sessions.max_active_sessions}")
        
        # Validate LLM config
        if self.llm.timeout_seconds <= 0:
            errors.append(f"Invalid LLM timeout: {self.llm.timeout_seconds}")
        
        if not (0.0 <= self.llm.temperature <= 2.0):
            errors.append(f"Invalid LLM temperature: {self.llm.temperature}")
        
        # Validate logging level
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.logging.level.upper() not in valid_levels:
            errors.append(f"Invalid log level: {self.logging.level}")
        
        # Validate secret key in production
        if not self.debug and self.security.secret_key == "dev-key-change-in-production":
            errors.append("Secret key must be changed in production")
        
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "debug": self.debug,
            "host": self.host,
            "port": self.port,
            "directories": {
                "uploads": self.directories.uploads,
                "outputs": self.directories.outputs,
                "temp": self.directories.temp,
                "logs": self.directories.logs
            },
            "sessions": {
                "timeout_hours": self.sessions.timeout_hours,
                "max_active_sessions": self.sessions.max_active_sessions,
                "cleanup_interval_minutes": self.sessions.cleanup_interval_minutes
            },
            "files": {
                "max_file_size_mb": self.files.max_file_size_mb,
                "max_files_per_upload": self.files.max_files_per_upload,
                "allowed_extensions": self.files.allowed_extensions
            },
            "llm": {
                "provider": self.llm.provider,
                "model": self.llm.model,
                "base_url": self.llm.base_url,
                "timeout_seconds": self.llm.timeout_seconds,
                "temperature": self.llm.temperature
            },
            "security": {
                "allowed_hosts": self.security.allowed_hosts,
                "cors_origins": self.security.cors_origins,
                "rate_limit_per_minute": self.security.rate_limit_per_minute
            }
        }


def _get_bool_env(key: str, default: bool) -> bool:
    """Get boolean environment variable."""
    value = os.getenv(key, "").lower()
    if value in ("true", "1", "yes", "on"):
        return True
    elif value in ("false", "0", "no", "off"):
        return False
    return default


def _get_int_env(key: str, default: int) -> int:
    """Get integer environment variable."""
    try:
        return int(os.getenv(key, str(default)))
    except ValueError:
        logger.warning(f"Invalid integer value for {key}, using default: {default}")
        return default


def _get_float_env(key: str, default: float) -> float:
    """Get float environment variable."""
    try:
        return float(os.getenv(key, str(default)))
    except ValueError:
        logger.warning(f"Invalid float value for {key}, using default: {default}")
        return default


# Global configuration instance
_config: Optional[AppConfig] = None


def get_config() -> AppConfig:
    """Get global configuration instance."""
    global _config
    if _config is None:
        _config = AppConfig.from_env()
        
        # Validate configuration
        errors = _config.validate()
        if errors:
            logger.error(f"Configuration validation errors: {errors}")
            raise ValueError(f"Invalid configuration: {'; '.join(errors)}")
        
        logger.info("Configuration loaded and validated successfully")
    
    return _config


def reload_config() -> AppConfig:
    """Reload configuration from environment."""
    global _config
    _config = None
    return get_config()
