"""
LangChain agent for generating JSON templates following expedition format specification.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

from langchain.agents import AgentExecutor, create_react_agent
from langchain.prompts import PromptTemplate
from langchain_ollama import <PERSON>llamaLLM

from ..config.langchain_config import AGENT_CONFIG, get_ollama_model_config
from ..tools.template_generator import create_template_generator_tool

logger = logging.getLogger(__name__)


class JSONGeneratorAgent:
    """Agent responsible for generating JSON templates from extracted expedition data."""

    def __init__(self):
        """Initialize the JSON generator agent."""
        self.llm = self._initialize_llm()
        self.tools = self._initialize_tools()
        self.agent_executor = self._create_agent()

    def _initialize_llm(self) -> OllamaLLM:
        """Initialize the Ollama LLM."""
        config = get_ollama_model_config()
        return OllamaLLM(
            model=config["model"],
            base_url=config["base_url"],
            temperature=config["temperature"],
            num_ctx=config["num_ctx"],
        )

    def _initialize_tools(self) -> List:
        """Initialize tools for the agent."""
        return [create_template_generator_tool()]

    def _create_agent(self) -> AgentExecutor:
        """Create the JSON generator agent."""
        prompt = PromptTemplate.from_template("""
You are an expert expedition JSON template generator. Your task is to create structured JSON files that follow the EXACT expedition format specification.

You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action (must be valid JSON)
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

CRITICAL INSTRUCTIONS:
1. ALWAYS use the expedition data provided in the question
2. NEVER use empty data like {{"days": []}}
3. Extract the expedition data from the question text and use it in the template_generator tool
4. The expedition data will be provided as "Expedition Data: {{...}}" in the question

TEMPLATE GENERATOR TOOL USAGE:
When using template_generator, you MUST:
- Use the EXACT expedition data provided in the question
- Use the EXACT operation type specified
- Use the EXACT location specified
- Use the EXACT output path specified

EXAMPLE of correct tool usage:
If the question contains "Expedition Data: {{\\"location\\": \\"Test Location\\", \\"activities\\": [\\"landing\\"]}}",
then use:
Action: template_generator
Action Input: {{"extracted_data": "{{\\"location\\": \\"Test Location\\", \\"activities\\": [\\"landing\\"]}}", "operation_type": "combined", "location": "Test Location", "output_path": "./output"}}

CRITICAL: After using the template_generator tool ONCE successfully, provide your Final Answer immediately.
Do not call the tool multiple times. When you see "Successfully generated JSON template:" in the output, the task is complete.

Question: {input}
{agent_scratchpad}
""")

        agent = create_react_agent(self.llm, self.tools, prompt)

        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=AGENT_CONFIG["verbose"],
            max_iterations=2,  # Limit to 2 iterations to prevent loops
            max_execution_time=AGENT_CONFIG["max_execution_time"],
            return_intermediate_steps=AGENT_CONFIG["return_intermediate_steps"],
            early_stopping_method=AGENT_CONFIG.get("early_stopping_method", "force"),
            handle_parsing_errors=AGENT_CONFIG.get("handle_parsing_errors", True),
        )

    def generate_json_templates(
        self, extracted_data: Dict[str, Any], pattern_analysis: str, output_dir: str
    ) -> Dict[str, Any]:
        """
        Generate JSON templates for different operation types.

        Args:
            extracted_data: Extracted expedition data
            pattern_analysis: Pattern analysis insights to include
            output_dir: Directory to save generated JSON files

        Returns:
            Dictionary containing generation results and file paths
        """
        try:
            logger.info("Generating JSON templates for expedition data")

            # Determine operation types present in the data
            operation_types = self._identify_operation_types(extracted_data)

            generated_files = {}

            # Generate JSON for each operation type
            for op_type in operation_types:
                logger.info(f"Generating JSON template for operation type: {op_type}")

                # Filter data for this operation type
                filtered_data = self._filter_data_for_operation_type(
                    extracted_data, op_type
                )

                # Add pattern analysis to the data
                filtered_data["pattern_analysis"] = pattern_analysis

                # Generate JSON template
                result = self._generate_single_template(
                    filtered_data, op_type, output_dir
                )

                if result.get("success"):
                    generated_files[op_type] = result["file_path"]
                else:
                    logger.error(
                        f"Failed to generate template for {op_type}: {result.get('error')}"
                    )

            return {
                "success": True,
                "generated_files": generated_files,
                "operation_types": operation_types,
                "total_files": len(generated_files),
            }

        except Exception as e:
            logger.error(f"Error generating JSON templates: {e}")
            return {"success": False, "error": str(e), "generated_files": {}}

    def generate_single_json_template(
        self,
        extracted_data: Dict[str, Any],
        operation_type: str,
        location: str,
        pattern_analysis: str,
        output_dir: str,
    ) -> Dict[str, Any]:
        """
        Generate a single JSON template for a specific operation type.

        Args:
            extracted_data: Extracted expedition data
            operation_type: Type of operation (am_only, pm_only, combined)
            location: Operation location
            pattern_analysis: Pattern analysis insights
            output_dir: Directory to save the JSON file

        Returns:
            Dictionary containing generation result
        """
        try:
            logger.info(
                f"Generating single JSON template for {operation_type} at {location}"
            )

            # Prepare data with pattern analysis
            data_with_analysis = extracted_data.copy()
            data_with_analysis["pattern_analysis"] = pattern_analysis

            # Generate the template
            result = self._generate_single_template(
                data_with_analysis, operation_type, output_dir, location
            )

            return result

        except Exception as e:
            logger.error(f"Error generating single JSON template: {e}")
            return {"success": False, "error": str(e)}

    def validate_and_enhance_json(
        self, json_file_path: str, pattern_analysis: str
    ) -> Dict[str, Any]:
        """
        Validate and enhance an existing JSON file with pattern analysis.

        Args:
            json_file_path: Path to existing JSON file
            pattern_analysis: Pattern analysis to add

        Returns:
            Dictionary containing validation and enhancement results
        """
        try:
            logger.info(f"Validating and enhancing JSON file: {json_file_path}")

            # Load existing JSON
            with open(json_file_path, encoding="utf-8") as f:
                json_data = json.load(f)

            input_text = f"""
            Validate and enhance this expedition JSON template:

            File: {json_file_path}
            JSON Data: {json.dumps(json_data, indent=2)}

            Pattern Analysis to Add: {pattern_analysis}

            Tasks:
            1. Validate the JSON against expedition format specification
            2. Check for missing required fields
            3. Verify data format compliance (dates, times, etc.)
            4. Add the pattern analysis as an "analysis" field in each day
            5. Enhance any incomplete sections
            6. Ensure proper structure and validation

            Return validation results and the enhanced JSON.
            """

            result = self.agent_executor.invoke({"input": input_text})

            return self._parse_validation_result(result, json_file_path)

        except Exception as e:
            logger.error(f"Error validating JSON file {json_file_path}: {e}")
            return {"success": False, "error": str(e), "file_path": json_file_path}

    def _identify_operation_types(self, extracted_data: Dict[str, Any]) -> List[str]:
        """Identify operation types present in the extracted data."""
        operation_types = []

        # Check if data contains multiple days or operation types
        days = extracted_data.get("days", [])
        if not days and "date" in extracted_data:
            # Single day data
            days = [extracted_data]

        for day in days:
            op_type = day.get("operation_type", "combined")
            if op_type not in operation_types:
                operation_types.append(op_type)

        # If no operation types identified, default to combined
        if not operation_types:
            operation_types = ["combined"]

        return operation_types

    def _filter_data_for_operation_type(
        self, extracted_data: Dict[str, Any], operation_type: str
    ) -> Dict[str, Any]:
        """Filter extracted data for a specific operation type using memory-efficient shallow copy."""

        # Create shallow copy for most fields to avoid memory overhead
        filtered_data = {
            "date": extracted_data.get("date"),
            "location": extracted_data.get("location"),
            "operation_type": operation_type,
            "notes": extracted_data.get("notes"),
            "weather": extracted_data.get("weather"),
        }

        # Share references for large collections (don't copy)
        if "groups" in extracted_data:
            filtered_data["groups"] = extracted_data["groups"]  # Share reference
        if "schedule" in extracted_data:
            filtered_data["schedule"] = extracted_data["schedule"]  # Share reference
        if "equipment" in extracted_data:
            filtered_data["equipment"] = extracted_data["equipment"]  # Share reference

        # Log the rich data we're preserving
        self._log_rich_data_preservation(filtered_data, operation_type)

        # If data contains multiple days, filter for the specific operation type
        days = extracted_data.get("days", [])
        if days:
            filtered_days = [
                day for day in days if day.get("operation_type") == operation_type
            ]
            if filtered_days:
                filtered_data["days"] = filtered_days
                # Preserve rich data from all days even if filtering
                self._preserve_rich_data_across_days(filtered_data, extracted_data)
        else:
            # Single day data - ensure operation type is set but preserve all other data
            filtered_data["operation_type"] = operation_type
            # Ensure we don't lose any rich data fields
            self._ensure_rich_data_preservation(filtered_data, extracted_data)

        return filtered_data

    def _log_rich_data_preservation(self, data: Dict[str, Any], operation_type: str):
        """Log information about rich data being preserved."""
        groups_count = len(data.get("groups", []))
        schedule_count = len(data.get("schedule", []))
        equipment_present = bool(data.get("equipment"))
        personnel_present = bool(data.get("personnel"))

        logger.info(
            f"Preserving rich data for {operation_type}: "
            f"groups={groups_count}, schedule={schedule_count}, "
            f"equipment={equipment_present}, personnel={personnel_present}"
        )

    def _preserve_rich_data_across_days(
        self, filtered_data: Dict[str, Any], original_data: Dict[str, Any]
    ):
        """Preserve rich data across multiple days when filtering."""
        # Aggregate rich data from all days
        all_groups = []
        all_schedule = []
        all_tides = []

        for day in original_data.get("days", []):
            all_groups.extend(day.get("groups", []))
            all_schedule.extend(day.get("schedule", []))
            all_tides.extend(day.get("tides", []))

        # Preserve aggregated data in the filtered result
        if all_groups and not filtered_data.get("groups"):
            filtered_data["groups"] = all_groups
        if all_schedule and not filtered_data.get("schedule"):
            filtered_data["schedule"] = all_schedule
        if all_tides and not filtered_data.get("tides"):
            filtered_data["tides"] = all_tides

    def _ensure_rich_data_preservation(
        self, filtered_data: Dict[str, Any], original_data: Dict[str, Any]
    ):
        """Ensure rich data fields are preserved when filtering single day data."""
        rich_data_fields = [
            "groups",
            "schedule",
            "tides",
            "equipment",
            "personnel",
            "weather",
            "notes",
        ]

        for field in rich_data_fields:
            if original_data.get(field):
                if field not in filtered_data or not filtered_data[field]:
                    filtered_data[field] = original_data[field]
                    logger.debug(f"Preserved {field} data during filtering")

    def _generate_single_template(
        self,
        data: Dict[str, Any],
        operation_type: str,
        output_dir: str,
        location: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Generate a single JSON template using the template generator tool."""
        try:
            # Extract location if not provided
            if not location:
                location = data.get("location", "Unknown_Location")

            # Always try direct tool call first (more reliable and faster)
            logger.info(f"Using direct template generation for {location}")
            return self._generate_template_direct(
                data, operation_type, location, output_dir
            )

        except Exception as e:
            logger.error(f"Error generating single template: {e}")
            return {
                "success": False,
                "error": str(e),
                "operation_type": operation_type,
                "location": location or "Unknown Location",
            }

    def _generate_template_direct(
        self, data: Dict[str, Any], operation_type: str, location: str, output_dir: str
    ) -> Dict[str, Any]:
        """Generate template by calling the tool directly."""
        try:
            import re

            from ..tools.template_generator import TemplateGeneratorTool

            # Create tool instance
            tool = TemplateGeneratorTool()

            # Prepare data as JSON string
            data_json = json.dumps(data, default=str)

            # Call tool directly
            result = tool._run(
                extracted_data=data_json,
                operation_type=operation_type,
                location=location,
                output_path=output_dir,
            )

            # Improved file path extraction using regex
            file_path_match = re.search(
                r"(?:Successfully generated.*?:\s*)([^\s]+\.json)", result
            )
            if file_path_match:
                file_path = file_path_match.group(1).strip()
                return {
                    "success": True,
                    "operation_type": operation_type,
                    "location": location,
                    "file_path": file_path,
                    "method": "direct_tool_call",
                }
            else:
                return {
                    "success": False,
                    "error": f"Tool call failed: {result}",
                    "operation_type": operation_type,
                    "location": location,
                }

        except Exception as e:
            logger.error(f"Direct template generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "operation_type": operation_type,
                "location": location,
            }

    def _generate_template_with_agent(
        self, data: Dict[str, Any], operation_type: str, location: str, output_dir: str
    ) -> Dict[str, Any]:
        """Generate template using the LLM agent (fallback method)."""
        try:
            # Prepare data as JSON string for the tool
            data_json = json.dumps(data, default=str)

            # Create input for the agent
            input_text = f"""
Generate a JSON template for expedition data using the template_generator tool.

REQUIRED PARAMETERS:
- Operation Type: {operation_type}
- Location: {location}
- Output Directory: {output_dir}

EXPEDITION DATA TO USE:
{data_json}

INSTRUCTIONS:
1. Use the template_generator tool with the EXACT expedition data provided above
2. Use operation_type: "{operation_type}"
3. Use location: "{location}"
4. Use output_path: "{output_dir}"
5. Pass the expedition data exactly as: extracted_data: "{data_json}"

Call the template_generator tool now with these parameters.
            """

            # Generate template using the agent executor
            result = self.agent_executor.invoke({"input": input_text})

            # Parse the result to extract the generated template information
            return self._parse_generation_result(result, operation_type, location)

        except Exception as e:
            logger.error(f"Agent-based template generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "operation_type": operation_type,
                "location": location,
            }

    def _create_complete_template(
        self, operation_type: str, location: str
    ) -> Dict[str, Any]:
        """Create a complete JSON template with all required fields."""
        from datetime import datetime

        # Generate realistic data
        base_date = datetime.now()
        weekday = base_date.strftime("%A")
        date_str = base_date.strftime("%Y-%m-%d")

        # Determine activity type and duration based on operation type
        if operation_type == "am_only":
            activity_type = "2h Guided Landing (Zodiac Shuttle)"
            groups = [
                {
                    "groupName": "Blue",
                    "color": "Blue",
                    "departureTime": "08:00",
                    "returnTime": "10:00",
                    "activity": "Guided Landing",
                },
                {
                    "groupName": "Red",
                    "color": "Red",
                    "departureTime": "08:15",
                    "returnTime": "10:15",
                    "activity": "Guided Landing",
                },
            ]
        elif operation_type == "pm_only":
            activity_type = "2h Guided Landing (Zodiac Shuttle)"
            groups = [
                {
                    "groupName": "Yellow",
                    "color": "Yellow",
                    "departureTime": "14:00",
                    "returnTime": "16:00",
                    "activity": "Guided Landing",
                },
                {
                    "groupName": "Green",
                    "color": "Green",
                    "departureTime": "14:15",
                    "returnTime": "16:15",
                    "activity": "Guided Landing",
                },
            ]
        else:  # combined
            activity_type = "Full Day Operations"
            groups = [
                {
                    "groupName": "Blue",
                    "color": "Blue",
                    "departureTime": "08:00",
                    "returnTime": "10:00",
                    "activity": "Guided Landing",
                },
                {
                    "groupName": "Red",
                    "color": "Red",
                    "departureTime": "08:15",
                    "returnTime": "10:15",
                    "activity": "Guided Landing",
                },
                {
                    "groupName": "Yellow",
                    "color": "Yellow",
                    "departureTime": "10:30",
                    "returnTime": "12:30",
                    "activity": "Guided Landing",
                },
                {
                    "groupName": "Green",
                    "color": "Green",
                    "departureTime": "10:45",
                    "returnTime": "12:45",
                    "activity": "Guided Landing",
                },
            ]

        return {
            "dayNumber": 2,
            "weekday": weekday,
            "date": date_str,
            "location": location,
            "utcOffset": "+08:00",
            "notes": "Landing operations weather dependent\n\nEquipment:\n• Interpretation boards\n• First aid kit\n• Radio communication\n• Collection bags\n\nSetup Notes:\n• Check tide conditions\n• Establish safety perimeter\n• Set up interpretation station",
            "zodiacs": 8,
            "twins": 1,
            "activityType": activity_type,
            "groups": groups,
            "schedule": self._generate_schedule(groups, location),
            "tides": [
                {"time": "02:00", "height": 2.0, "label": "Low Tide"},
                {"time": "08:30", "height": 12.0, "label": "High Tide"},
            ],
            "groupOrder": ["Blue", "Red", "Yellow", "Green"],
            "zodiacDrivers": [
                {
                    "zodiacNumber": 1,
                    "wave1Drivers": ["team-member-3"],
                    "wave2Drivers": ["team-member-7"],
                },
                {
                    "zodiacNumber": 2,
                    "wave1Drivers": ["team-member-4"],
                    "wave2Drivers": ["team-member-8"],
                },
            ],
            "landingGuides": {
                "wave1Guides": ["team-member-1"],
                "wave2Guides": ["team-member-2"],
            },
            "nextDayLocation": "Next Location",
            "sunTimes": {
                "twilightStart": "05:30",
                "sunrise": "06:00",
                "sunset": "18:30",
                "twilightEnd": "19:00",
            },
        }

    def _generate_schedule(
        self, groups: List[Dict], location: str
    ) -> List[Dict[str, Any]]:
        """Generate schedule events based on groups."""
        schedule = [
            {
                "time": "07:40",
                "type": "arrival",
                "description": f"Scheduled arrival time in {location}",
                "location": location,
            },
            {
                "time": "08:00",
                "type": "drop_zodiacs",
                "description": "Drop 8 Zodiac + 1 Twin",
                "zodiacsCount": 8,
                "twinsCount": 1,
            },
        ]

        # Add group events
        for group in groups:
            schedule.append(
                {
                    "time": group["departureTime"],
                    "type": "disembark",
                    "description": f"{group['groupName']} group disembark for {group['activity']}",
                    "group": group,
                }
            )
            schedule.append(
                {
                    "time": group["returnTime"],
                    "type": "return",
                    "description": f"{group['groupName']} returns",
                    "group": {"groupName": group["groupName"], "color": group["color"]},
                }
            )

        # Add standard events
        schedule.extend(
            [
                {
                    "time": "12:45",
                    "type": "last_zodiac",
                    "description": "Last zodiac returns",
                },
                {
                    "time": "18:00",
                    "type": "ship_depart",
                    "description": "Ship departs",
                    "headingTo": "Next Location",
                },
                {
                    "time": "18:30",
                    "type": "recap_briefing",
                    "description": "Daily recap and tomorrow's briefing",
                    "presenter": "Expedition Leader",
                    "title": "Daily recap and tomorrow's briefing",
                },
                {
                    "time": "19:30",
                    "type": "naturalist_dinner",
                    "description": "Naturalist Dinner",
                },
            ]
        )

        return schedule

    def _parse_generation_result(
        self, result: Dict[str, Any], operation_type: str, location: str
    ) -> Dict[str, Any]:
        """Parse the agent's generation result."""
        try:
            output = result.get("output", "")

            # Look for file path in the output
            file_path = None
            for line in output.split("\n"):
                if "json" in line.lower() and (
                    "generated" in line.lower() or "saved" in line.lower()
                ):
                    # Try to extract file path
                    import re

                    path_match = re.search(r"([^\s]+\.json)", line)
                    if path_match:
                        file_path = path_match.group(1)
                        break

            success = "success" in output.lower() and "error" not in output.lower()

            return {
                "success": success,
                "operation_type": operation_type,
                "location": location,
                "file_path": file_path,
                "agent_output": output,
                "intermediate_steps": result.get("intermediate_steps", []),
            }

        except Exception as e:
            logger.error(f"Error parsing generation result: {e}")
            return {
                "success": False,
                "error": str(e),
                "operation_type": operation_type,
                "location": location,
            }

    def _parse_validation_result(
        self, result: Dict[str, Any], json_file_path: str
    ) -> Dict[str, Any]:
        """Parse validation and enhancement result."""
        try:
            output = result.get("output", "")

            # Try to extract enhanced JSON from output
            enhanced_json = None
            try:
                # Look for JSON in the output
                import re

                json_match = re.search(r"\{.*\}", output, re.DOTALL)
                if json_match:
                    enhanced_json = json.loads(json_match.group())
            except json.JSONDecodeError:
                pass

            validation_passed = (
                "valid" in output.lower() and "error" not in output.lower()
            )

            return {
                "success": True,
                "validation_passed": validation_passed,
                "file_path": json_file_path,
                "enhanced_json": enhanced_json,
                "validation_notes": output,
                "intermediate_steps": result.get("intermediate_steps", []),
            }

        except Exception as e:
            logger.error(f"Error parsing validation result: {e}")
            return {"success": False, "error": str(e), "file_path": json_file_path}

    def create_generation_summary(self, generation_results: Dict[str, Any]) -> str:
        """Create a summary of JSON generation results."""
        if not generation_results.get("success"):
            return f"JSON generation failed: {generation_results.get('error', 'Unknown error')}"

        generated_files = generation_results.get("generated_files", {})
        operation_types = generation_results.get("operation_types", [])

        summary = f"""
JSON Template Generation Summary
===============================

Total Files Generated: {len(generated_files)}
Operation Types: {", ".join(operation_types)}

Generated Files:
"""

        for op_type, file_path in generated_files.items():
            summary += f"\n{op_type.replace('_', ' ').title()}: {file_path}"

        summary += f"""

Status: Successfully generated {len(generated_files)} JSON templates
Format: Expedition format specification compliant
Features: Includes pattern analysis, validation, and complete operational data
"""

        return summary
