"""
Lang<PERSON>hain agent for analyzing operational patterns in expedition data.
"""

import json
import logging
from typing import Any, Dict, List, Optional

from langchain.agents import AgentExecutor, create_react_agent
from langchain.prompts import PromptTemplate
from langchain_ollama import OllamaLLM

from ..config.langchain_config import (
    AGENT_CONFIG,
    PATTERN_ANALYSIS_PROMPTS,
    get_ollama_model_config,
)
from ..tools.pattern_detector import create_pattern_detector_tool

logger = logging.getLogger(__name__)


class PatternAnalysisAgent:
    """Agent responsible for analyzing operational patterns and generating insights."""

    def __init__(self):
        """Initialize the pattern analysis agent."""
        self._check_ollama_availability()
        self.llm = self._initialize_llm()
        self.tools = self._initialize_tools()
        self.agent_executor = self._create_agent()
        self.analysis_prompts = PATTERN_ANALYSIS_PROMPTS

    def _check_ollama_availability(self):
        """Check if Ollama server is running and accessible."""
        import requests
        from requests.exceptions import RequestException

        config = get_ollama_model_config()
        base_url = config["base_url"]

        try:
            # Try to connect to Ollama server
            response = requests.get(f"{base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                logger.info("Ollama server is running and accessible")
                return True
        except RequestException as e:
            logger.error(f"Ollama server is not accessible: {e}")
            logger.error(f"Please ensure Ollama is installed and running at {base_url}")
            logger.error("You can install Ollama from https://ollama.ai/")
            logger.error("After installation, run 'ollama serve' to start the server")
            # Continue anyway, as the _initialize_llm method will handle errors

    def _initialize_llm(self) -> OllamaLLM:
        """Initialize the Ollama LLM with fallback options."""
        config = get_ollama_model_config()

        # List of fallback models in case the primary model isn't available
        fallback_models = [
            config["model"],  # Try the configured model first
            "llama3.1:8b",  # Llama 3.1 8B
            "gemma3:12b",  # Gemma 3 12B
            "mistral:latest",  # Mistral latest
            "qwen3-8b-gguf:latest",  # Qwen 3 8B
            "meta-llama-3-8b-gguf:latest",  # Meta Llama 3 8B
            "mistral-7b-v0-1-gguf:latest",  # Original model as last resort
        ]

        # Try each model until one works
        last_error = None
        for model in fallback_models:
            try:
                logger.info(f"Attempting to initialize Ollama with model: {model}")
                return OllamaLLM(
                    model=model,
                    base_url=config["base_url"],
                    temperature=config["temperature"],
                    num_ctx=config["num_ctx"],
                )
            except Exception as e:
                last_error = e
                logger.warning(f"Failed to initialize model {model}: {e}")
                continue

        # If we get here, none of the models worked
        logger.error(f"Failed to initialize any Ollama model: {last_error}")
        raise RuntimeError(f"Could not initialize any Ollama model: {last_error}")

    def _initialize_tools(self) -> List:
        """Initialize tools for the agent."""
        return [create_pattern_detector_tool()]

    def _create_agent(self) -> AgentExecutor:
        """Create the pattern analysis agent."""
        prompt = PromptTemplate.from_template("""
You are an expert expedition operations analyst. Your task is to analyze expedition data to identify operational patterns and generate insights that explain operational decisions.

You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action (must be valid JSON)
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

IMPORTANT: When using the pattern_detector tool, you MUST provide both parameters in JSON format:
{{"expedition_data": "JSON string of expedition data", "analysis_type": "all"}}

The analysis_type must be one of: "duration", "timing", "operational", or "all"

EXAMPLE of correct tool usage:
Action: pattern_detector
Action Input: {{"expedition_data": "{{\\"days\\": []}}", "analysis_type": "all"}}

Your analysis goals:
1. Identify why certain operations are AM-only or PM-only
2. Explain why some operations have shorter durations (e.g., 2 hours vs full day)
3. Discover operational patterns related to location, weather, tides, and equipment
4. Generate insights about personnel allocation and safety considerations
5. Provide actionable recommendations for future operations

Focus on providing clear explanations for operational decisions based on:
- Environmental factors (weather, tides, daylight)
- Logistical constraints (equipment, personnel, vessel schedules)
- Safety considerations
- Location-specific requirements
- Guest experience optimization

IMPORTANT: After using the pattern_detector tool ONCE, provide your Final Answer immediately. Do not call the tool multiple times.

Question: {input}
{agent_scratchpad}
""")

        agent = create_react_agent(self.llm, self.tools, prompt)

        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=AGENT_CONFIG["verbose"],
            max_iterations=2,  # Limit to 2 iterations to prevent loops
            max_execution_time=AGENT_CONFIG["max_execution_time"],
            return_intermediate_steps=AGENT_CONFIG["return_intermediate_steps"],
            early_stopping_method=AGENT_CONFIG.get("early_stopping_method", "force"),
            handle_parsing_errors=AGENT_CONFIG.get("handle_parsing_errors", True),
        )

    def analyze_operational_patterns(
        self, expedition_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Analyze operational patterns in expedition data.

        Args:
            expedition_data: Complete expedition data to analyze

        Returns:
            Dictionary containing pattern analysis results and insights
        """
        try:
            logger.info("Starting comprehensive operational pattern analysis")

            # Check if we have enough data for meaningful analysis
            days = expedition_data.get("days", [])
            if not days:
                # Check if this is a single operation
                if "location" in expedition_data:
                    days = [expedition_data]
                    expedition_data = {"days": days}

            # Validate we have enough operations for meaningful analysis
            if len(days) < 2:
                logger.warning(
                    "Insufficient data for meaningful pattern analysis (less than 2 operations)"
                )
                return {
                    "success": True,
                    "limited_data": True,
                    "analysis": "Insufficient data for meaningful pattern analysis. Pattern analysis requires multiple operations to compare.",
                    "recommendation": "Collect more operation data (at least 3-5 operations) for meaningful pattern analysis.",
                }

            # Prepare data for analysis
            processed_data = self._preprocess_data_for_analysis(expedition_data)

            # Convert data to JSON string for the agent
            data_json = json.dumps(processed_data, default=str)

            # Create a more structured prompt for better analysis
            input_text = f"""
            Analyze the operational patterns across these {len(days)} expedition operations and provide comprehensive insights:

            {data_json}

            ANALYSIS INSTRUCTIONS:
            Focus on identifying PATTERNS across multiple operations, not just describing individual operations.
            
            1. Duration Pattern Analysis:
               - COMPARE operations of different durations (2h vs 4h vs full-day)
               - IDENTIFY factors that consistently influence duration decisions
               - FIND correlations between location characteristics and duration
               - DETERMINE if certain activities always require longer durations

            2. Timing Pattern Analysis:
               - COMPARE AM-only vs PM-only vs full-day operations
               - IDENTIFY consistent patterns in timing decisions
               - CORRELATE tide information with operation timing
               - DETERMINE if certain locations have consistent timing preferences
               - ANALYZE how weather conditions influence timing decisions

            3. Operational Pattern Analysis:
               - IDENTIFY equipment allocation patterns across different operation types
               - COMPARE personnel requirements for different locations and activities
               - FIND safety considerations that consistently influence operations
               - DETERMINE location-specific operational requirements that appear consistently

            4. Provide SPECIFIC EVIDENCE-BASED explanations for:
               - Why certain locations consistently have shorter/longer operations
               - Why specific locations are consistently scheduled for AM vs PM
               - How equipment allocation varies predictably by location or activity
               - How personnel deployment follows consistent patterns

            IMPORTANT: Base all insights on ACTUAL PATTERNS in the data, not general knowledge.
            For each insight, cite specific examples from the operations data.
            """

            # Check if we have tools available
            if len(self.tools) == 0:
                # Fallback to direct LLM analysis without tools
                logger.info("No tools available, using direct LLM analysis")
                result = self.llm.invoke(input_text)

                # Create a structured analysis result
                analysis_result = self._structure_analysis_from_text(
                    result, processed_data
                )
            else:
                # Try to use agent executor with tools, with fallback to direct LLM
                try:
                    result = self.agent_executor.invoke({"input": input_text})
                    # Parse and structure the analysis result
                    analysis_result = self._parse_analysis_result(
                        result, processed_data
                    )
                except Exception as tool_error:
                    logger.warning(
                        f"Tool-based analysis failed: {tool_error}, falling back to direct LLM"
                    )
                    try:
                        # First try direct LLM analysis
                        result = self.llm.invoke(input_text)
                        analysis_result = self._structure_analysis_from_text(
                            result, processed_data
                        )
                        analysis_result["method"] = "direct_llm_fallback"
                        analysis_result["tool_error"] = str(tool_error)
                    except Exception as llm_error:
                        logger.error(f"Direct LLM analysis also failed: {llm_error}")
                        # Fall back to rule-based analysis if LLM is completely unavailable
                        analysis_result = self._fallback_rule_based_analysis(
                            days, processed_data
                        )
                        analysis_result["method"] = "rule_based_fallback"
                        analysis_result["llm_error"] = str(llm_error)
                        analysis_result["tool_error"] = str(tool_error)

            logger.info("Operational pattern analysis completed successfully")
            return analysis_result

        except Exception as e:
            logger.error(f"Error analyzing operational patterns: {e}")
            return {"success": False, "error": str(e), "analysis": {}}

    def _fallback_rule_based_analysis(
        self, days: List[Dict], processed_data: Dict
    ) -> Dict[str, Any]:
        """Provide rule-based pattern analysis when LLM is unavailable."""
        try:
            logger.info("Using rule-based fallback analysis")

            # Calculate summary statistics
            summary = self._calculate_summary_statistics(days)

            # Try to import pattern detector for direct analysis
            try:
                from ..tools.pattern_detector import PatternDetectorTool

                detector = PatternDetectorTool()

                # Analyze patterns using the detector with proper error handling
                try:
                    duration_patterns = detector._analyze_duration_patterns(
                        {"days": days}
                    )
                except Exception as e:
                    logger.error(f"Error analyzing duration patterns: {e}")
                    duration_patterns = {
                        "insights": [
                            "Duration pattern analysis failed due to data format issues."
                        ]
                    }

                try:
                    timing_patterns = detector._analyze_timing_patterns({"days": days})
                except Exception as e:
                    logger.error(f"Error analyzing timing patterns: {e}")
                    timing_patterns = {
                        "insights": [
                            "Timing pattern analysis failed due to data format issues."
                        ]
                    }

                try:
                    operational_patterns = detector._analyze_operational_patterns(
                        {"days": days}
                    )
                except Exception as e:
                    logger.error(f"Error analyzing operational patterns: {e}")
                    operational_patterns = {
                        "insights": [
                            "Operational pattern analysis failed due to data format issues."
                        ]
                    }

                # Combine insights
                all_insights = []
                all_insights.extend(duration_patterns.get("insights", []))
                all_insights.extend(timing_patterns.get("insights", []))
                all_insights.extend(operational_patterns.get("insights", []))

                # Create structured analysis
                analysis_text = "# Pattern Analysis\n\n"

                # Duration patterns
                analysis_text += "## Duration Patterns\n"
                for insight in duration_patterns.get("insights", []):
                    analysis_text += f"- {insight}\n"

                # Timing patterns
                analysis_text += "\n## Timing Patterns\n"
                for insight in timing_patterns.get("insights", []):
                    analysis_text += f"- {insight}\n"

                # Operational patterns
                analysis_text += "\n## Operational Patterns\n"
                for insight in operational_patterns.get("insights", []):
                    analysis_text += f"- {insight}\n"

                # Structure the analysis
                return self._structure_analysis_from_text(
                    analysis_text,
                    {
                        "summary": summary,
                        "duration_patterns": duration_patterns,
                        "timing_patterns": timing_patterns,
                        "operational_patterns": operational_patterns,
                    },
                )

            except Exception as detector_error:
                logger.error(f"Error using pattern detector: {detector_error}")

                # Create a minimal analysis with just the summary statistics
                return {
                    "success": True,
                    "method": "statistics_only",
                    "analysis": "Statistical analysis of operational patterns.",
                    "patterns_detected": {
                        "duration_patterns": "Statistical analysis shows variations in operation durations.",
                        "timing_patterns": "Statistical analysis shows preferences for AM or PM operations.",
                        "operational_patterns": "Statistical analysis shows patterns in resource allocation.",
                    },
                    "summary_statistics": summary,
                }

        except Exception as fallback_error:
            logger.error(f"Error in fallback analysis: {fallback_error}")

            # Return a minimal valid response
            return {
                "success": True,
                "method": "minimal_fallback",
                "analysis": "Basic statistical analysis of operational patterns.",
                "error": str(fallback_error),
            }

    def _preprocess_data_for_analysis(
        self, expedition_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Preprocess expedition data to make it more suitable for pattern analysis."""
        processed_data = {"days": []}

        # Get the days data
        days = expedition_data.get("days", [])
        if not days and "location" in expedition_data:
            # This is a single operation
            days = [expedition_data]

        # Process each day/operation
        for day in days:
            processed_day = {}

            # Extract key fields for analysis
            for key in [
                "location",
                "date",
                "operation_type",
                "arrival_time",
                "departure_time",
                "zodiacs",
                "twins",
                "notes",
                "weather",
            ]:
                if key in day:
                    processed_day[key] = day[key]

            # Process groups
            if "groups" in day and isinstance(day["groups"], list):
                processed_day["groups"] = []
                for group in day["groups"]:
                    if isinstance(group, dict):
                        processed_group = {}
                        for key in [
                            "groupName",
                            "color",
                            "departureTime",
                            "returnTime",
                            "activity",
                        ]:
                            if key in group:
                                processed_group[key] = group[key]
                        processed_day["groups"].append(processed_group)

            # Process schedule
            if "schedule" in day and isinstance(day["schedule"], list):
                processed_day["schedule"] = []
                for item in day["schedule"]:
                    if isinstance(item, dict):
                        processed_item = {}
                        for key in ["time", "type", "description", "location"]:
                            if key in item:
                                processed_item[key] = item[key]
                        processed_day["schedule"].append(processed_item)

            # Process tides
            if "tides" in day and isinstance(day["tides"], list):
                processed_day["tides"] = []
                for tide in day["tides"]:
                    if isinstance(tide, dict):
                        processed_tide = {}
                        for key in ["time", "height", "label"]:
                            if key in tide:
                                processed_tide[key] = tide[key]
                        processed_day["tides"].append(processed_tide)

            # Calculate operation duration if not present
            if "arrival_time" in processed_day and "departure_time" in processed_day:
                try:
                    from datetime import datetime

                    arrival = datetime.strptime(processed_day["arrival_time"], "%H:%M")
                    departure = datetime.strptime(
                        processed_day["departure_time"], "%H:%M"
                    )

                    # Handle overnight operations
                    if departure < arrival:
                        departure = departure.replace(day=arrival.day + 1)

                    duration_hours = (departure - arrival).total_seconds() / 3600
                    processed_day["duration_hours"] = round(duration_hours, 1)

                    # Categorize duration
                    if duration_hours <= 3:
                        processed_day["duration_category"] = "short"
                    elif duration_hours <= 5:
                        processed_day["duration_category"] = "medium"
                    else:
                        processed_day["duration_category"] = "full_day"
                except:
                    pass

            # Add to processed days
            processed_data["days"].append(processed_day)

        # Add summary statistics
        processed_data["summary"] = self._calculate_summary_statistics(
            processed_data["days"]
        )

        return processed_data

    def _calculate_summary_statistics(
        self, days: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Calculate summary statistics for pattern analysis."""
        summary = {
            "total_operations": len(days),
            "locations": {},
            "operation_types": {"am_only": 0, "pm_only": 0, "combined": 0},
            "duration_categories": {"short": 0, "medium": 0, "full_day": 0},
        }

        # Count by location
        for day in days:
            location = day.get("location", "Unknown")
            if location not in summary["locations"]:
                summary["locations"][location] = 0
            summary["locations"][location] += 1

            # Count by operation type
            op_type = day.get("operation_type", "combined")
            if op_type in summary["operation_types"]:
                summary["operation_types"][op_type] += 1

            # Count by duration category
            duration_cat = day.get("duration_category")
            if duration_cat and duration_cat in summary["duration_categories"]:
                summary["duration_categories"][duration_cat] += 1

        return summary

    def _structure_analysis_from_text(
        self, analysis_text: str, data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Structure the analysis text into a more organized format."""
        # Extract different sections from the analysis text
        sections = {
            "duration_patterns": self._extract_section(
                analysis_text, ["duration pattern", "duration analysis"]
            ),
            "timing_patterns": self._extract_section(
                analysis_text, ["timing pattern", "timing analysis", "am vs pm"]
            ),
            "operational_patterns": self._extract_section(
                analysis_text, ["operational pattern", "operational analysis"]
            ),
            "recommendations": self._extract_section(
                analysis_text, ["recommendation", "suggest", "advice"]
            ),
        }

        # Create structured result
        result = {
            "success": True,
            "method": "direct_llm",
            "analysis": analysis_text,
            "patterns_detected": sections,
            "summary_statistics": data.get("summary", {}),
        }

        return result

    def _extract_section(self, text: str, keywords: List[str]) -> str:
        """Extract a section from text based on keywords."""
        import re

        # Try to find sections with headers
        for keyword in keywords:
            pattern = rf"(?i)(?:^|\n)(?:\d+\.\s*)?({keyword}[^\n]*?)(?:\n|:)(.*?)(?:\n\s*\n|\n\s*\d+\.|\Z)"
            matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
            if matches:
                # Return the first matching section
                return f"{matches[0][0]}:\n{matches[0][1].strip()}"

        # If no section headers found, look for paragraphs containing keywords
        for keyword in keywords:
            paragraphs = re.split(r"\n\s*\n", text)
            for para in paragraphs:
                if re.search(keyword, para, re.IGNORECASE):
                    return para.strip()

        # Return empty string if no matching section found
        return ""

    def analyze_duration_patterns(
        self, expedition_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Analyze why operations have different durations.

        Args:
            expedition_data: Expedition data to analyze

        Returns:
            Dictionary containing duration pattern analysis
        """
        try:
            logger.info("Analyzing duration patterns")

            data_json = json.dumps(expedition_data, default=str)

            input_text = f"""
            Focus specifically on duration patterns in this expedition data:

            {data_json}

            Analyze and explain:
            1. Why some operations are only 2 hours vs full day operations
            2. What environmental factors limit operation duration
            3. How location characteristics influence timing
            4. Equipment or logistical constraints affecting duration
            5. Safety considerations that require shorter operations
            6. Guest experience factors in duration decisions

            Provide specific examples and clear explanations for duration choices.
            """

            result = self.agent_executor.invoke({"input": input_text})

            return {
                "success": True,
                "analysis_type": "duration_patterns",
                "insights": result["output"],
                "intermediate_steps": result.get("intermediate_steps", []),
            }

        except Exception as e:
            logger.error(f"Error analyzing duration patterns: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_type": "duration_patterns",
            }

    def analyze_timing_preferences(
        self, expedition_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Analyze why operations are scheduled for AM-only or PM-only.

        Args:
            expedition_data: Expedition data to analyze

        Returns:
            Dictionary containing timing preference analysis
        """
        try:
            logger.info("Analyzing timing preferences")

            data_json = json.dumps(expedition_data, default=str)

            input_text = f"""
            Focus specifically on timing preferences in this expedition data:

            {data_json}

            Analyze and explain:
            1. Why certain operations are scheduled for AM-only
            2. Why certain operations are scheduled for PM-only
            3. How tide timing influences operation scheduling
            4. Weather pattern considerations in timing decisions
            5. Vessel schedule constraints affecting timing
            6. Wildlife activity patterns influencing timing
            7. Light conditions and their impact on operations

            Provide location-specific timing explanations with supporting evidence.
            """

            result = self.agent_executor.invoke({"input": input_text})

            return {
                "success": True,
                "analysis_type": "timing_preferences",
                "insights": result["output"],
                "intermediate_steps": result.get("intermediate_steps", []),
            }

        except Exception as e:
            logger.error(f"Error analyzing timing preferences: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_type": "timing_preferences",
            }

    def generate_operational_insights(
        self, expedition_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate comprehensive operational insights and recommendations.

        Args:
            expedition_data: Expedition data to analyze

        Returns:
            Dictionary containing operational insights and recommendations
        """
        try:
            logger.info("Generating operational insights")

            data_json = json.dumps(expedition_data, default=str)

            input_text = f"""
            Generate comprehensive operational insights from this expedition data:

            {data_json}

            Provide insights on:
            1. Equipment usage patterns and optimization opportunities
            2. Personnel allocation efficiency and recommendations
            3. Safety pattern analysis and risk mitigation strategies
            4. Location-specific operational best practices
            5. Seasonal or weather-related operational adaptations
            6. Guest experience optimization based on operational patterns
            7. Resource utilization efficiency
            8. Operational risk factors and mitigation strategies

            Format your response as actionable insights with specific recommendations.
            """

            result = self.agent_executor.invoke({"input": input_text})

            return {
                "success": True,
                "analysis_type": "operational_insights",
                "insights": result["output"],
                "recommendations": self._extract_recommendations(result["output"]),
                "intermediate_steps": result.get("intermediate_steps", []),
            }

        except Exception as e:
            logger.error(f"Error generating operational insights: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_type": "operational_insights",
            }

    def create_pattern_analysis_report(self, expedition_data: Dict[str, Any]) -> str:
        """
        Create a comprehensive pattern analysis report.

        Args:
            expedition_data: Expedition data to analyze

        Returns:
            Formatted analysis report as string
        """
        try:
            logger.info("Creating comprehensive pattern analysis report")

            # Extract days from expedition data for summary
            days = expedition_data.get("days", [])
            if (
                not days
                and isinstance(expedition_data, dict)
                and "location" in expedition_data
            ):
                days = [expedition_data]

            # Calculate basic statistics for fallback
            summary = self._calculate_summary_statistics(days)

            try:
                # Try to perform all types of analysis
                duration_analysis = self.analyze_duration_patterns(expedition_data)
                timing_analysis = self.analyze_timing_preferences(expedition_data)
                operational_insights = self.generate_operational_insights(
                    expedition_data
                )

                # Create comprehensive report
                report = self._format_comprehensive_report(
                    duration_analysis, timing_analysis, operational_insights
                )

                # Check if the report has actual content
                if (
                    "No duration insights available" in report
                    and "No timing insights available" in report
                    and "No operational insights available" in report
                ):
                    # If all sections are empty, use fallback report
                    logger.warning(
                        "All analysis sections were empty, using fallback report"
                    )
                    report = self._create_fallback_report(expedition_data, summary)

                return report

            except Exception as analysis_error:
                logger.error(
                    f"Error in standard analysis: {analysis_error}, using fallback report"
                )
                return self._create_fallback_report(expedition_data, summary)

        except Exception as e:
            logger.error(f"Error creating pattern analysis report: {e}")
            # Return a minimal report even in case of error
            return f"""
EXPEDITION OPERATIONAL PATTERN ANALYSIS REPORT
==============================================

EXECUTIVE SUMMARY
-----------------
Analysis of expedition operational patterns was attempted but encountered an error: {e}

BASIC STATISTICS
---------------
Total operations analyzed: {len(expedition_data.get("days", []))}

RECOMMENDATIONS
--------------
1. Review the data format to ensure it contains valid operational information
2. Check for missing required fields such as location, date, and schedule
3. Try analyzing a smaller subset of operations if the dataset is very large

"""

    def _parse_analysis_result(
        self, result: Dict[str, Any], expedition_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Parse the agent's analysis result into structured format."""
        try:
            output = result.get("output", "")

            return {
                "success": True,
                "analysis": {
                    "full_analysis": output,
                    "duration_insights": self._extract_section(output, "duration"),
                    "timing_insights": self._extract_section(output, "timing"),
                    "operational_insights": self._extract_section(
                        output, "operational"
                    ),
                    "recommendations": self._extract_recommendations(output),
                },
                "data_summary": {
                    "total_operations": len(expedition_data.get("days", [])),
                    "locations": list(
                        {
                            day.get("location", "")
                            for day in expedition_data.get("days", [])
                        }
                    ),
                    "analysis_date": json.dumps(expedition_data, default=str)[:100]
                    + "...",
                },
                "intermediate_steps": result.get("intermediate_steps", []),
            }

        except Exception as e:
            logger.error(f"Error parsing analysis result: {e}")
            return {"success": False, "error": str(e), "analysis": {}}

    def _extract_section(self, text: str, section_type: str) -> str:
        """Extract a specific section from the analysis text."""
        lines = text.split("\n")
        section_lines = []
        in_section = False

        for line in lines:
            if section_type.lower() in line.lower() and any(
                keyword in line.lower()
                for keyword in ["analysis", "pattern", "insight"]
            ):
                in_section = True
                section_lines.append(line)
            elif in_section:
                if line.strip() and not line.startswith(" "):
                    # New section started
                    if any(
                        keyword in line.lower()
                        for keyword in [
                            "analysis",
                            "pattern",
                            "insight",
                            "recommendation",
                        ]
                    ):
                        break
                section_lines.append(line)

        return "\n".join(section_lines).strip()

    def _extract_recommendations(self, text: str) -> List[str]:
        """Extract recommendations from analysis text."""
        recommendations = []
        lines = text.split("\n")

        for line in lines:
            line = line.strip()
            if line.startswith(("•", "-", "*")) or any(
                keyword in line.lower()
                for keyword in ["recommend", "suggest", "should", "consider"]
            ):
                recommendations.append(line.lstrip("•-* "))

        return recommendations[:10]  # Limit to top 10 recommendations

    def _create_fallback_report(
        self, expedition_data: Dict[str, Any], summary: Dict[str, Any]
    ) -> str:
        """Create a fallback report based on statistical analysis when LLM analysis fails."""
        # Extract days from expedition data
        days = expedition_data.get("days", [])
        if (
            not days
            and isinstance(expedition_data, dict)
            and "location" in expedition_data
        ):
            days = [expedition_data]

        # Get unique locations
        locations = set()
        for day in days:
            if isinstance(day, dict) and "location" in day and day["location"]:
                locations.add(day["location"])

        # Count operation types
        am_operations = 0
        pm_operations = 0
        full_day_operations = 0

        for day in days:
            if not isinstance(day, dict):
                continue

            operation_type = day.get("operation_type", "")
            if operation_type == "am_only":
                am_operations += 1
            elif operation_type == "pm_only":
                pm_operations += 1
            else:
                full_day_operations += 1

        # Count equipment usage
        zodiac_operations = 0
        twin_operations = 0

        for day in days:
            if not isinstance(day, dict):
                continue

            if day.get("zodiacs", 0) > 0:
                zodiac_operations += 1
            if day.get("twins", 0) > 0:
                twin_operations += 1

        # Generate the report
        report = f"""
EXPEDITION OPERATIONAL PATTERN ANALYSIS REPORT
==============================================

EXECUTIVE SUMMARY
-----------------
This report provides a statistical analysis of {len(days)} expedition operations across {len(locations)} unique locations.

STATISTICAL OVERVIEW
-------------------
- Total Operations: {len(days)}
- Unique Locations: {len(locations)}
- AM-Only Operations: {am_operations}
- PM-Only Operations: {pm_operations}
- Full-Day Operations: {full_day_operations}

DURATION PATTERN ANALYSIS
-------------------------
Based on statistical analysis of the operational data:

1. {full_day_operations} operations ({full_day_operations / max(1, len(days)) * 100:.1f}%) were full-day operations
2. {am_operations + pm_operations} operations ({(am_operations + pm_operations) / max(1, len(days)) * 100:.1f}%) were half-day operations
3. The most common operation type was {"full-day" if full_day_operations >= am_operations and full_day_operations >= pm_operations else "AM-only" if am_operations >= pm_operations else "PM-only"}

TIMING PREFERENCE ANALYSIS
--------------------------
Timing preferences show:

1. {am_operations} operations ({am_operations / max(1, len(days)) * 100:.1f}%) were scheduled for morning (AM)
2. {pm_operations} operations ({pm_operations / max(1, len(days)) * 100:.1f}%) were scheduled for afternoon (PM)
3. {full_day_operations} operations ({full_day_operations / max(1, len(days)) * 100:.1f}%) were scheduled for full day

OPERATIONAL INSIGHTS & RECOMMENDATIONS
-------------------------------------
Equipment utilization patterns:

1. Zodiacs were used in {zodiac_operations} operations ({zodiac_operations / max(1, len(days)) * 100:.1f}%)
2. Twin boats were used in {twin_operations} operations ({twin_operations / max(1, len(days)) * 100:.1f}%)

RECOMMENDATIONS
--------------
1. Consider balancing AM and PM operations more evenly
2. Evaluate equipment utilization efficiency across different operation types
3. Analyze location-specific requirements for better resource allocation
4. Review scheduling patterns to optimize guest experience
"""

        return report

    def _format_comprehensive_report(
        self,
        duration_analysis: Dict[str, Any],
        timing_analysis: Dict[str, Any],
        operational_insights: Dict[str, Any],
    ) -> str:
        """Format a comprehensive analysis report."""
        report = """
EXPEDITION OPERATIONAL PATTERN ANALYSIS REPORT
==============================================

EXECUTIVE SUMMARY
-----------------
This report analyzes operational patterns in expedition data to explain why certain operations have specific durations, timing preferences, and operational characteristics.

"""

        # Duration Analysis Section
        if duration_analysis.get("success"):
            report += """
DURATION PATTERN ANALYSIS
-------------------------
"""
            report += duration_analysis.get(
                "insights", "No duration insights available."
            )
            report += "\n\n"

        # Timing Analysis Section
        if timing_analysis.get("success"):
            report += """
TIMING PREFERENCE ANALYSIS
--------------------------
"""
            report += timing_analysis.get("insights", "No timing insights available.")
            report += "\n\n"

        # Operational Insights Section
        if operational_insights.get("success"):
            report += """
OPERATIONAL INSIGHTS & RECOMMENDATIONS
-------------------------------------
"""
            report += operational_insights.get(
                "insights", "No operational insights available."
            )

            recommendations = operational_insights.get("recommendations", [])
            if recommendations:
                report += "\n\nKEY RECOMMENDATIONS:\n"
                for i, rec in enumerate(recommendations, 1):
                    report += f"{i}. {rec}\n"

        report += """

CONCLUSION
----------
This analysis provides insights into operational decision-making patterns that can inform future expedition planning and optimization efforts.
"""

        return report
