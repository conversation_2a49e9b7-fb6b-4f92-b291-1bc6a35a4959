"""
Data Validation Service for Expedition Planner.
Handles validation and cleaning of extracted expedition data.
"""

import logging
import re
from datetime import datetime, time
from typing import Any, Dict, List, Optional, Tuple

from ..config.expedition_config import ValidationConfig
from ..utils.error_handlers import ValidationError, handle_expedition_errors

logger = logging.getLogger(__name__)


class DataValidator:
    """Service for validating and cleaning expedition data."""

    def __init__(self, config: ValidationConfig):
        """Initialize the data validator."""
        self.config = config
        self.time_pattern = re.compile(self.config.time_format_regex)
        self.date_pattern = re.compile(self.config.date_format_regex)

    @handle_expedition_errors
    def validate_and_clean(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and clean extracted expedition data.

        Args:
            data: Raw extracted data

        Returns:
            Cleaned and validated data

        Raises:
            ValidationError: If validation fails
        """
        try:
            if not isinstance(data, dict):
                raise ValidationError("Data must be a dictionary")

            # Create a copy to avoid modifying original
            cleaned_data = data.copy()

            # Validate and clean each section
            cleaned_data = self._ensure_required_fields(cleaned_data)
            cleaned_data = self._validate_and_clean_basic_fields(cleaned_data)
            cleaned_data = self._validate_and_clean_groups(cleaned_data)
            cleaned_data = self._validate_and_clean_schedule(cleaned_data)
            cleaned_data = self._validate_and_clean_tides(cleaned_data)
            cleaned_data = self._validate_and_clean_equipment(cleaned_data)
            cleaned_data = self._validate_and_clean_personnel(cleaned_data)

            logger.info("Successfully validated and cleaned expedition data")
            return cleaned_data

        except ValidationError:
            raise
        except Exception as e:
            raise ValidationError(f"Unexpected error during validation: {e}")

    def _ensure_required_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure all required fields are present."""
        for field in self.config.required_fields:
            if field not in data or data[field] is None or data[field] == "":
                if field == "date":
                    data[field] = datetime.now().strftime("%Y-%m-%d")
                elif field == "location":
                    data[field] = "Unknown Location"
                elif field == "activities":
                    data[field] = []
                else:
                    data[field] = ""

                logger.warning(f"Missing required field '{field}', using default value")

        return data

    def _validate_and_clean_basic_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean basic fields like date, times, etc."""
        # Validate date
        if data.get("date"):
            if not self.date_pattern.match(data["date"]):
                logger.warning(
                    f"Invalid date format: {data['date']}, attempting to fix"
                )
                data["date"] = self._fix_date_format(data["date"])

        # Validate times
        time_fields = ["arrival_time", "departure_time"]
        for field in time_fields:
            if data.get(field):
                if not self.time_pattern.match(data[field]):
                    logger.warning(
                        f"Invalid time format for {field}: {data[field]}, attempting to fix"
                    )
                    data[field] = self._fix_time_format(data[field])

        # Validate operation type
        valid_operation_types = ["am_only", "pm_only", "combined"]
        if (
            data.get("operation_type")
            and data["operation_type"] not in valid_operation_types
        ):
            logger.warning(
                f"Invalid operation type: {data['operation_type']}, defaulting to 'combined'"
            )
            data["operation_type"] = "combined"

        # Clean text fields
        text_fields = ["location", "weather", "notes"]
        for field in text_fields:
            if data.get(field) and isinstance(data[field], str):
                data[field] = data[field].strip()

        return data

    def _validate_and_clean_groups(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean groups data."""
        if "groups" not in data:
            data["groups"] = []
            return data

        if not isinstance(data["groups"], list):
            logger.warning("Groups field is not a list, converting to empty list")
            data["groups"] = []
            return data

        # Limit number of groups
        if len(data["groups"]) > self.config.max_groups_per_operation:
            logger.warning(
                f"Too many groups ({len(data['groups'])}), limiting to {self.config.max_groups_per_operation}"
            )
            data["groups"] = data["groups"][: self.config.max_groups_per_operation]

        # Validate each group
        cleaned_groups = []
        for i, group in enumerate(data["groups"]):
            if not isinstance(group, dict):
                logger.warning(f"Group {i} is not a dictionary, skipping")
                continue

            cleaned_group = self._clean_group(group)
            if cleaned_group:
                cleaned_groups.append(cleaned_group)

        data["groups"] = cleaned_groups
        return data

    def _clean_group(self, group: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Clean a single group entry."""
        cleaned = {}

        # Required fields for a group
        required_group_fields = ["groupName", "color"]
        for field in required_group_fields:
            if field not in group or not group[field]:
                logger.warning(f"Group missing required field: {field}")
                return None
            cleaned[field] = str(group[field]).strip()

        # Optional time fields
        time_fields = ["departureTime", "returnTime"]
        for field in time_fields:
            if group.get(field):
                if self.time_pattern.match(group[field]):
                    cleaned[field] = group[field]
                else:
                    fixed_time = self._fix_time_format(group[field])
                    if fixed_time:
                        cleaned[field] = fixed_time

        # Activity field
        if group.get("activity"):
            cleaned["activity"] = str(group["activity"]).strip()

        return cleaned

    def _validate_and_clean_schedule(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean schedule data."""
        if "schedule" not in data:
            data["schedule"] = []
            return data

        if not isinstance(data["schedule"], list):
            logger.warning("Schedule field is not a list, converting to empty list")
            data["schedule"] = []
            return data

        # Limit number of schedule events
        if len(data["schedule"]) > self.config.max_schedule_events:
            logger.warning(
                f"Too many schedule events ({len(data['schedule'])}), limiting to {self.config.max_schedule_events}"
            )
            data["schedule"] = data["schedule"][: self.config.max_schedule_events]

        # Validate each schedule event
        cleaned_schedule = []
        for i, event in enumerate(data["schedule"]):
            if not isinstance(event, dict):
                logger.warning(f"Schedule event {i} is not a dictionary, skipping")
                continue

            cleaned_event = self._clean_schedule_event(event)
            if cleaned_event:
                cleaned_schedule.append(cleaned_event)

        data["schedule"] = cleaned_schedule
        return data

    def _clean_schedule_event(self, event: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Clean a single schedule event."""
        cleaned = {}

        # Time is required
        if not event.get("time"):
            logger.warning("Schedule event missing time field")
            return None

        if self.time_pattern.match(event["time"]):
            cleaned["time"] = event["time"]
        else:
            fixed_time = self._fix_time_format(event["time"])
            if not fixed_time:
                logger.warning(f"Could not fix time format: {event['time']}")
                return None
            cleaned["time"] = fixed_time

        # Event type
        valid_types = [
            "arrival",
            "departure",
            "briefing",
            "disembarkation",
            "zodiac_drop",
            "custom",
        ]
        if event.get("type") and event["type"] in valid_types:
            cleaned["type"] = event["type"]
        else:
            cleaned["type"] = "custom"

        # Description
        if event.get("description"):
            cleaned["description"] = str(event["description"]).strip()

        # Group (optional)
        if event.get("group"):
            cleaned["group"] = str(event["group"]).strip()

        return cleaned

    def _validate_and_clean_tides(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean tides data."""
        if "tides" not in data:
            data["tides"] = []
            return data

        if not isinstance(data["tides"], list):
            logger.warning("Tides field is not a list, converting to empty list")
            data["tides"] = []
            return data

        # Validate each tide entry
        cleaned_tides = []
        for i, tide in enumerate(data["tides"]):
            if not isinstance(tide, dict):
                logger.warning(f"Tide entry {i} is not a dictionary, skipping")
                continue

            cleaned_tide = self._clean_tide_entry(tide)
            if cleaned_tide:
                cleaned_tides.append(cleaned_tide)

        data["tides"] = cleaned_tides
        return data

    def _clean_tide_entry(self, tide: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Clean a single tide entry."""
        cleaned = {}

        # Time is required
        if not tide.get("time"):
            logger.warning("Tide entry missing time field")
            return None

        if self.time_pattern.match(tide["time"]):
            cleaned["time"] = tide["time"]
        else:
            fixed_time = self._fix_time_format(tide["time"])
            if not fixed_time:
                logger.warning(f"Could not fix tide time format: {tide['time']}")
                return None
            cleaned["time"] = fixed_time

        # Height validation
        if tide.get("height") is not None:
            try:
                height = float(tide["height"])
                min_height, max_height = self.config.tide_height_range
                if min_height <= height <= max_height:
                    cleaned["height"] = height
                else:
                    logger.warning(
                        f"Tide height {height} outside valid range {self.config.tide_height_range}"
                    )
                    return None
            except (ValueError, TypeError):
                logger.warning(f"Invalid tide height value: {tide['height']}")
                return None

        # Label
        if tide.get("label"):
            cleaned["label"] = str(tide["label"]).strip()

        return cleaned

    def _validate_and_clean_equipment(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean equipment data."""
        if "equipment" not in data:
            data["equipment"] = {"zodiacs": 0, "twins": 0, "other": []}
            return data

        if not isinstance(data["equipment"], dict):
            logger.warning("Equipment field is not a dictionary, using default")
            data["equipment"] = {"zodiacs": 0, "twins": 0, "other": []}
            return data

        equipment = data["equipment"]

        # Validate numeric fields
        numeric_fields = ["zodiacs", "twins"]
        for field in numeric_fields:
            if field in equipment:
                try:
                    equipment[field] = max(0, int(equipment[field]))
                except (ValueError, TypeError):
                    logger.warning(
                        f"Invalid equipment count for {field}: {equipment[field]}"
                    )
                    equipment[field] = 0
            else:
                equipment[field] = 0

        # Validate other equipment list
        if "other" not in equipment:
            equipment["other"] = []
        elif not isinstance(equipment["other"], list):
            logger.warning(
                "Equipment 'other' field is not a list, converting to empty list"
            )
            equipment["other"] = []

        return data

    def _validate_and_clean_personnel(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean personnel data."""
        if "personnel" not in data:
            data["personnel"] = {"total_count": 0, "guides": [], "drivers": []}
            return data

        if not isinstance(data["personnel"], dict):
            logger.warning("Personnel field is not a dictionary, using default")
            data["personnel"] = {"total_count": 0, "guides": [], "drivers": []}
            return data

        personnel = data["personnel"]

        # Validate total count
        if "total_count" in personnel:
            try:
                personnel["total_count"] = max(0, int(personnel["total_count"]))
            except (ValueError, TypeError):
                logger.warning(
                    f"Invalid personnel total count: {personnel['total_count']}"
                )
                personnel["total_count"] = 0
        else:
            personnel["total_count"] = 0

        # Validate lists
        list_fields = ["guides", "drivers"]
        for field in list_fields:
            if field not in personnel:
                personnel[field] = []
            elif not isinstance(personnel[field], list):
                logger.warning(
                    f"Personnel {field} field is not a list, converting to empty list"
                )
                personnel[field] = []

        return data

    def _fix_date_format(self, date_str: str) -> str:
        """Attempt to fix date format."""
        try:
            # Try common date formats
            formats = ["%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y", "%d-%m-%Y", "%Y/%m/%d"]

            for fmt in formats:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime("%Y-%m-%d")
                except ValueError:
                    continue

            # If all else fails, use current date
            logger.warning(f"Could not parse date: {date_str}, using current date")
            return datetime.now().strftime("%Y-%m-%d")

        except Exception:
            return datetime.now().strftime("%Y-%m-%d")

    def _fix_time_format(self, time_str: str) -> Optional[str]:
        """Attempt to fix time format."""
        try:
            # Remove common suffixes and clean
            time_str = time_str.strip().upper()
            time_str = re.sub(r"[^\d:APM]", "", time_str)

            # Handle AM/PM format
            is_pm = "PM" in time_str
            is_am = "AM" in time_str
            time_str = re.sub(r"[APM]", "", time_str)

            # Try to parse time
            if ":" in time_str:
                parts = time_str.split(":")
                if len(parts) == 2:
                    try:
                        hour = int(parts[0])
                        minute = int(parts[1])

                        # Handle AM/PM conversion
                        if is_pm and hour < 12:
                            hour += 12
                        elif is_am and hour == 12:
                            hour = 0

                        # Validate ranges
                        if 0 <= hour <= 23 and 0 <= minute <= 59:
                            return f"{hour:02d}:{minute:02d}"
                    except ValueError:
                        pass

            logger.warning(f"Could not fix time format: {time_str}")
            return None

        except Exception:
            return None
