"""
Text Processing Service for Expedition Planner.
Handles text extraction, preprocessing, and structured data extraction.
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Protocol

from ..config.expedition_config import ProcessingConfig
from ..utils.error_handlers import DocumentExtractionError, handle_expedition_errors

logger = logging.getLogger(__name__)


class TextExtractorInterface(Protocol):
    """Protocol for text extraction implementations."""

    def extract_text(self, file_path: str) -> str:
        """Extract text from a file."""
        ...


class TextProcessor:
    """Service for processing and extracting structured data from text."""

    def __init__(
        self, text_extractor: TextExtractorInterface, config: ProcessingConfig
    ):
        """Initialize the text processor."""
        self.text_extractor = text_extractor
        self.config = config
        self._compile_regex_patterns()

    def _compile_regex_patterns(self):
        """Pre-compile regex patterns for performance."""
        # Date patterns
        self.date_patterns = [
            re.compile(r"\b(\d{1,2})[/-](\d{1,2})[/-](\d{4})\b"),
            re.compile(r"\b(\d{4})[/-](\d{1,2})[/-](\d{1,2})\b"),
            re.compile(
                r"\b(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{4})\b",
                re.IGNORECASE,
            ),
        ]

        # Time patterns
        self.time_patterns = [
            re.compile(r"\b(\d{1,2}):(\d{2})\s*(AM|PM)?\b", re.IGNORECASE),
            re.compile(r"\b(\d{1,2})\.(\d{2})\s*(AM|PM)?\b", re.IGNORECASE),
            re.compile(r"\b(\d{1,2})\s*(AM|PM)\b", re.IGNORECASE),
        ]

        # Location patterns
        self.location_patterns = [
            re.compile(
                r"(?:location|site|place|destination):\s*([^\n\r]+)", re.IGNORECASE
            ),
            re.compile(
                r"(?:at|in|near)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)", re.IGNORECASE
            ),
        ]

        # Group/color patterns
        self.group_patterns = [
            re.compile(
                r"\b(red|blue|green|yellow|orange|purple|pink|white|black|brown)\s+group\b",
                re.IGNORECASE,
            ),
            re.compile(
                r"\bgroup\s+(red|blue|green|yellow|orange|purple|pink|white|black|brown)\b",
                re.IGNORECASE,
            ),
            re.compile(
                r"\b(red|blue|green|yellow|orange|purple|pink|white|black|brown)\s+team\b",
                re.IGNORECASE,
            ),
        ]

        # Tide patterns
        self.tide_patterns = [
            re.compile(
                r"(?:low|high)\s+tide\s+(?:at\s+)?(\d{1,2}):(\d{2})", re.IGNORECASE
            ),
            re.compile(r"tide\s+(\d{1,2}):(\d{2})\s+(\d+\.?\d*)\s*m?", re.IGNORECASE),
        ]

        # Equipment patterns - more specific patterns for accurate counts
        self.equipment_patterns = [
            re.compile(r"(\d+)\s+zodiac[s]?", re.IGNORECASE),
            re.compile(r"zodiac[s]?\s*:\s*(\d+)", re.IGNORECASE),
            re.compile(r"zodiac[s]?\s*count\s*:\s*(\d+)", re.IGNORECASE),
            re.compile(r"(\d+)\s+twin[s]?", re.IGNORECASE),
            re.compile(r"twin[s]?\s*:\s*(\d+)", re.IGNORECASE),
            re.compile(r"twin[s]?\s*count\s*:\s*(\d+)", re.IGNORECASE),
            re.compile(r"(\d+)\s+boat[s]?", re.IGNORECASE),
            re.compile(r"boat[s]?\s*:\s*(\d+)", re.IGNORECASE),
        ]

    @handle_expedition_errors
    def extract_text_from_files(self, file_paths: List[str]) -> List[str]:
        """
        Extract text from multiple files.

        Args:
            file_paths: List of file paths to process

        Returns:
            List of extracted text strings

        Raises:
            DocumentExtractionError: If extraction fails
        """
        try:
            extracted_texts = []
            failed_files = []

            for file_path in file_paths:
                try:
                    text = self.text_extractor.extract_text(file_path)
                    if text and len(text.strip()) > 10:
                        extracted_texts.append(text)
                        logger.info(f"Successfully extracted text from: {file_path}")
                    else:
                        failed_files.append(file_path)
                        logger.warning(
                            f"No meaningful text extracted from: {file_path}"
                        )

                except Exception as e:
                    failed_files.append(file_path)
                    logger.error(f"Failed to extract text from {file_path}: {e}")

            if not extracted_texts:
                raise DocumentExtractionError(
                    f"No text could be extracted from any of the {len(file_paths)} files",
                    {"failed_files": failed_files},
                )

            if failed_files:
                logger.warning(
                    f"Failed to extract text from {len(failed_files)} files: {failed_files}"
                )

            return extracted_texts

        except DocumentExtractionError:
            raise
        except Exception as e:
            raise DocumentExtractionError(
                f"Unexpected error during text extraction: {e}"
            )

    @handle_expedition_errors
    def extract_structured_data(self, text: str, location: str = "") -> Dict[str, Any]:
        """
        Extract structured data from text using regex patterns.

        Args:
            text: Text to process
            location: Optional location context

        Returns:
            Dictionary containing structured data

        Raises:
            DocumentExtractionError: If extraction fails
        """
        try:
            if not text or len(text.strip()) < 10:
                raise DocumentExtractionError(
                    "Insufficient text content for extraction"
                )

            # Initialize structured data
            structured_data = {
                "date": None,  # Use None instead of empty string
                "location": location or None,  # Use None instead of empty string
                "arrival_time": "",
                "departure_time": "",
                "operation_type": "combined",
                "groups": [],
                "schedule": [],
                "tides": [],
                "equipment": {
                    "zodiacs": None,
                    "twins": None,
                    "other": [],
                },  # Use None instead of 0
                "personnel": {
                    "total_count": None,
                    "guides": [],
                    "drivers": [],
                },  # Use None instead of 0
                "weather": "",
                "notes": "",
            }

            # Extract each type of data
            structured_data["date"] = self._extract_date(text)
            if not structured_data["location"]:
                structured_data["location"] = self._extract_location(text)

            times = self._extract_times(text)
            if times:
                structured_data["arrival_time"] = times[0] if len(times) > 0 else ""
                structured_data["departure_time"] = times[-1] if len(times) > 1 else ""

            structured_data["groups"] = self._extract_groups(text)
            structured_data["schedule"] = self._extract_schedule_events(text)
            structured_data["tides"] = self._extract_tides(text)
            structured_data["equipment"] = self._extract_equipment(text)
            structured_data["personnel"] = self._extract_personnel(text)
            structured_data["weather"] = self._extract_weather(text)
            structured_data["notes"] = self._extract_notes(text)

            # Determine operation type
            structured_data["operation_type"] = self._determine_operation_type(
                structured_data
            )

            logger.info("Successfully extracted structured data from text")
            return structured_data

        except DocumentExtractionError:
            raise
        except Exception as e:
            raise DocumentExtractionError(
                f"Unexpected error during structured extraction: {e}"
            )

    def _extract_date(self, text: str) -> Optional[str]:
        """Extract date from text."""
        # Track confidence for date extraction
        date_candidates = []

        for pattern in self.date_patterns:
            match = pattern.search(text)
            if match:
                try:
                    groups = match.groups()
                    if len(groups) == 3:
                        # Try different date formats
                        if len(groups[2]) == 4:  # Year is last
                            if int(groups[0]) > 12:  # DD/MM/YYYY
                                date_str = (
                                    f"{groups[2]}-{groups[1]:0>2}-{groups[0]:0>2}"
                                )
                                date_candidates.append(
                                    {
                                        "date": date_str,
                                        "confidence": 0.9,
                                        "source": f"DD/MM/YYYY: {match.group(0)}",
                                    }
                                )
                            else:  # MM/DD/YYYY
                                date_str = (
                                    f"{groups[2]}-{groups[0]:0>2}-{groups[1]:0>2}"
                                )
                                date_candidates.append(
                                    {
                                        "date": date_str,
                                        "confidence": 0.85,
                                        "source": f"MM/DD/YYYY: {match.group(0)}",
                                    }
                                )
                        elif len(groups[0]) == 4:  # YYYY/MM/DD
                            date_str = f"{groups[0]}-{groups[1]:0>2}-{groups[2]:0>2}"
                            date_candidates.append(
                                {
                                    "date": date_str,
                                    "confidence": 0.95,  # Highest confidence for ISO format
                                    "source": f"YYYY/MM/DD: {match.group(0)}",
                                }
                            )
                except (ValueError, IndexError):
                    continue

        # Look for month name patterns
        month_patterns = [
            r"(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(\d{4})",  # 15 January 2023
            r"(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(\d{1,2})[,\s]+(\d{4})",  # January 15, 2023
            r"(\d{1,2})(?:st|nd|rd|th)?\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(\d{4})",  # 15th January 2023
        ]

        month_map = {
            "Jan": "01",
            "Feb": "02",
            "Mar": "03",
            "Apr": "04",
            "May": "05",
            "Jun": "06",
            "Jul": "07",
            "Aug": "08",
            "Sep": "09",
            "Oct": "10",
            "Nov": "11",
            "Dec": "12",
        }

        for pattern in month_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    if len(match) == 3:
                        # Handle different formats
                        if match[0] in month_map:  # Month first (Jan 15, 2023)
                            month = month_map[match[0][:3].title()]
                            day = match[1].zfill(2)
                            year = match[2]
                        else:  # Day first (15 Jan 2023)
                            day = match[0].zfill(2)
                            month = month_map[match[1][:3].title()]
                            year = match[2]

                        # Validate day and month
                        day_int = int(day)
                        if 1 <= day_int <= 31:
                            date_str = f"{year}-{month}-{day}"
                            date_candidates.append(
                                {
                                    "date": date_str,
                                    "confidence": 0.9,
                                    "source": f"Month name: {match}",
                                }
                            )
                except (ValueError, IndexError, KeyError):
                    continue

        # If we found potential dates, return the one with highest confidence
        if date_candidates:
            # Sort by confidence (descending)
            date_candidates.sort(key=lambda x: x["confidence"], reverse=True)
            return date_candidates[0]["date"]

        # Return None instead of defaulting to today's date
        return None

    def _extract_times(self, text: str) -> List[str]:
        """Extract times from text."""
        times = []
        for pattern in self.time_patterns:
            matches = pattern.findall(text)
            for match in matches:
                try:
                    if isinstance(match, tuple):
                        if len(match) == 3:  # Hour, minute, AM/PM
                            hour, minute, ampm = match
                            hour = int(hour)
                            minute = int(minute) if minute else 0

                            if ampm and ampm.upper() == "PM" and hour < 12:
                                hour += 12
                            elif ampm and ampm.upper() == "AM" and hour == 12:
                                hour = 0

                            if 0 <= hour <= 23 and 0 <= minute <= 59:
                                times.append(f"{hour:02d}:{minute:02d}")
                        elif len(match) == 2:  # Hour, minute
                            hour, minute = match
                            hour = int(hour)
                            minute = int(minute)

                            if 0 <= hour <= 23 and 0 <= minute <= 59:
                                times.append(f"{hour:02d}:{minute:02d}")
                except (ValueError, IndexError):
                    continue

        return sorted(list(set(times)))  # Remove duplicates and sort

    def _extract_location(self, text: str) -> str:
        """Extract location from text."""
        for pattern in self.location_patterns:
            match = pattern.search(text)
            if match:
                location = match.group(1).strip()
                if len(location) > 2:
                    return location

        return "Unknown Location"

    def _extract_groups(self, text: str) -> List[Dict[str, Any]]:
        """Extract group information from text."""
        groups = []
        found_colors = set()

        # Track confidence for each group
        group_confidence = {}

        # First pass: Look for explicit group mentions with color
        for pattern in self.group_patterns:
            matches = pattern.findall(text)
            for match in matches:
                color = match.lower() if isinstance(match, str) else match[0].lower()
                if color not in found_colors:
                    found_colors.add(color)
                    group_data = {
                        "groupName": color.title(),
                        "color": color.title(),
                        "departureTime": "",
                        "returnTime": "",
                        "activity": "",
                        "_confidence": 0.9,  # High confidence for explicit color group mentions
                    }
                    groups.append(group_data)
                    group_confidence[color] = 0.9

        # Second pass: Look for group activity associations
        group_activity_patterns = [
            r"(?:group|team)\s+(\w+)\s+(?:will|to|for)\s+([^\.]+)",
            r"(\w+)\s+(?:group|team)\s+(?:will|to|for)\s+([^\.]+)",
        ]

        for pattern in group_activity_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                group_name = match[0].lower()
                activity = match[1].strip()

                # Check if this is a color we already found
                if group_name in found_colors:
                    # Find the group and update its activity
                    for group in groups:
                        if group["color"].lower() == group_name:
                            group["activity"] = activity
                            # Increase confidence since we found more evidence
                            group["_confidence"] = min(
                                1.0, group.get("_confidence", 0.9) + 0.1
                            )
                            group_confidence[group_name] = group["_confidence"]

                # Check if this is a color we haven't seen yet
                elif group_name in [
                    "red",
                    "blue",
                    "green",
                    "yellow",
                    "orange",
                    "purple",
                    "pink",
                    "white",
                    "black",
                    "brown",
                ]:
                    found_colors.add(group_name)
                    group_data = {
                        "groupName": group_name.title(),
                        "color": group_name.title(),
                        "departureTime": "",
                        "returnTime": "",
                        "activity": activity,
                        "_confidence": 0.85,  # Good confidence for color + activity
                    }
                    groups.append(group_data)
                    group_confidence[group_name] = 0.85

        # Third pass: Look for group time associations
        time_patterns = [
            r"(?:group|team)\s+(\w+)\s+(?:at|departure|return|leaves?|returns?)\s+(\d{1,2}:\d{2})",
            r"(\w+)\s+(?:group|team)\s+(?:at|departure|return|leaves?|returns?)\s+(\d{1,2}:\d{2})",
        ]

        for pattern in time_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                group_name = match[0].lower()
                time_str = match[1]

                # Check if this is a group we already found
                if group_name in found_colors:
                    # Find the group and update its time
                    for group in groups:
                        if group["color"].lower() == group_name:
                            # Determine if this is departure or return time based on context
                            context = match[0].lower()
                            if "return" in context:
                                group["returnTime"] = time_str
                            else:
                                group["departureTime"] = time_str

                            # Increase confidence since we found more evidence
                            group["_confidence"] = min(
                                1.0, group.get("_confidence", 0.9) + 0.1
                            )
                            group_confidence[group_name] = group["_confidence"]

        # Remove groups with low confidence
        filtered_groups = [
            group for group in groups if group.get("_confidence", 0) >= 0.7
        ]

        # Remove the confidence field before returning
        for group in filtered_groups:
            if "_confidence" in group:
                del group["_confidence"]

        return filtered_groups

    def _extract_schedule_events(self, text: str) -> List[Dict[str, Any]]:
        """Extract schedule events from text."""
        events = []

        # First pass: Look for explicit schedule items with time and description
        schedule_patterns = [
            r"(\d{1,2}[:.]\d{2}(?:\s*(?:AM|PM))?)\s*[-:]\s*([^\n\r\.]+)",
            r"at\s+(\d{1,2}[:.]\d{2}(?:\s*(?:AM|PM))?)\s+([^\n\r\.]+)",
            r"(\d{1,2}[:.]\d{2}(?:\s*(?:AM|PM))?)\s+([^\n\r\.]+)",
        ]

        for pattern in schedule_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                time_str = match[0].replace(".", ":")
                description = match[1].strip()

                # Normalize time format
                try:
                    # Handle AM/PM format
                    if re.search(r"(AM|PM)", time_str, re.IGNORECASE):
                        time_parts = re.match(
                            r"(\d{1,2})[:]?(\d{2})(?:\s*)(AM|PM)?",
                            time_str,
                            re.IGNORECASE,
                        )
                        if time_parts:
                            hour = int(time_parts.group(1))
                            minute = int(time_parts.group(2))
                            ampm = time_parts.group(3)

                            if ampm and ampm.upper() == "PM" and hour < 12:
                                hour += 12
                            elif ampm and ampm.upper() == "AM" and hour == 12:
                                hour = 0

                            time_str = f"{hour:02d}:{minute:02d}"
                    else:
                        # 24-hour format
                        time_parts = re.match(r"(\d{1,2})[:]?(\d{2})", time_str)
                        if time_parts:
                            hour = int(time_parts.group(1))
                            minute = int(time_parts.group(2))
                            time_str = f"{hour:02d}:{minute:02d}"
                except (ValueError, AttributeError):
                    continue

                # Determine event type based on description
                event_type = "custom"
                if re.search(
                    r"(?:arrival|arrive|dock|landing)", description, re.IGNORECASE
                ):
                    event_type = "arrival"
                elif re.search(
                    r"(?:departure|depart|leave|undock)", description, re.IGNORECASE
                ):
                    event_type = "departure"
                elif re.search(
                    r"(?:breakfast|lunch|dinner|meal)", description, re.IGNORECASE
                ):
                    event_type = "meal"
                elif re.search(
                    r"(?:briefing|meeting|gather)", description, re.IGNORECASE
                ):
                    event_type = "briefing"

                # Extract group information if present
                group = ""
                group_match = re.search(
                    r"(?:group|team)\s+(\w+)", description, re.IGNORECASE
                )
                if group_match:
                    group = group_match.group(1).title()

                # Add event with high confidence
                events.append(
                    {
                        "time": time_str,
                        "type": event_type,
                        "description": description,
                        "group": group,
                        "_confidence": 0.9,  # High confidence for explicit schedule items
                    }
                )

        # Second pass: Use extracted times if we don't have enough schedule items
        if len(events) < 2:
            times = self._extract_times(text)

            # Create basic events from extracted times
            for i, time_str in enumerate(times):
                # Skip times that are already in events
                if any(e["time"] == time_str for e in events):
                    continue

                event_type = "custom"
                description = None

                # Try to determine event type from context
                if i == 0:
                    event_type = "arrival"
                elif i == len(times) - 1:
                    event_type = "departure"

                # Only add events with actual times
                if time_str:
                    events.append(
                        {
                            "time": time_str,
                            "type": event_type,
                            "description": None,  # Use null instead of generic placeholders
                            "group": "",
                            "_confidence": 0.6,  # Lower confidence for inferred schedule items
                        }
                    )

        # Sort events by time
        events.sort(key=lambda x: x["time"])

        # Remove low confidence events if we have enough high confidence ones
        high_confidence_events = [e for e in events if e.get("_confidence", 0) >= 0.8]
        if len(high_confidence_events) >= 3:
            events = high_confidence_events

        # Remove confidence field before returning
        for event in events:
            if "_confidence" in event:
                del event["_confidence"]

        return events

    def _extract_tides(self, text: str) -> List[Dict[str, Any]]:
        """Extract tide information from text."""
        tides = []

        # Check if tide information is present at all
        has_tide_info = re.search(r"(?:tide|tidal)", text, re.IGNORECASE)
        if not has_tide_info:
            # Return empty array instead of fabricating tide data
            return []

        # First pass: Look for explicit tide information
        for pattern in self.tide_patterns:
            matches = pattern.findall(text)
            for match in matches:
                try:
                    if len(match) >= 2:
                        hour = int(match[0])
                        minute = int(match[1])
                        height = (
                            float(match[2]) if len(match) > 2 else None
                        )  # Use None if no height specified

                        if 0 <= hour <= 23 and 0 <= minute <= 59:
                            # Determine tide type (high/low) if possible
                            tide_type = "unknown"
                            context = text[
                                max(0, text.find(match[0]) - 20) : text.find(match[0])
                                + len(match[0])
                                + 20
                            ]

                            if re.search(r"high\s+tide", context, re.IGNORECASE):
                                tide_type = "high"
                            elif re.search(r"low\s+tide", context, re.IGNORECASE):
                                tide_type = "low"

                            tides.append(
                                {
                                    "time": f"{hour:02d}:{minute:02d}",
                                    "height": height,
                                    "type": tide_type,
                                    "label": f"{tide_type.title()} Tide"
                                    if tide_type != "unknown"
                                    else "Tide",
                                    "_confidence": 0.9,  # High confidence for explicit tide information
                                }
                            )
                except (ValueError, IndexError):
                    continue

        # Second pass: Look for more general tide mentions
        tide_mentions = [
            r"(?:high|low)\s+tide\s+(?:is|at|around|approximately)\s+(\d{1,2})[:\.]?(\d{2})",
            r"tide\s+(?:is|at|around|approximately)\s+(\d{1,2})[:\.]?(\d{2})",
        ]

        for pattern in tide_mentions:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    hour = int(match[0])
                    minute = int(match[1])

                    if 0 <= hour <= 23 and 0 <= minute <= 59:
                        # Determine tide type (high/low) if possible
                        tide_type = "unknown"
                        context = text[
                            max(0, text.find(match[0]) - 20) : text.find(match[0])
                            + len(match[0])
                            + 20
                        ]

                        if re.search(r"high\s+tide", context, re.IGNORECASE):
                            tide_type = "high"
                        elif re.search(r"low\s+tide", context, re.IGNORECASE):
                            tide_type = "low"

                        # Check if this time is already in our tides list
                        time_str = f"{hour:02d}:{minute:02d}"
                        if not any(t["time"] == time_str for t in tides):
                            tides.append(
                                {
                                    "time": time_str,
                                    "height": None,  # No height information
                                    "type": tide_type,
                                    "label": f"{tide_type.title()} Tide"
                                    if tide_type != "unknown"
                                    else "Tide",
                                    "_confidence": 0.8,  # Good confidence for general tide mentions
                                }
                            )
                except (ValueError, IndexError):
                    continue

        # Sort tides by time
        tides.sort(key=lambda x: x["time"])

        # Remove confidence field before returning
        for tide in tides:
            if "_confidence" in tide:
                del tide["_confidence"]

        return tides

    def _extract_equipment(self, text: str) -> Dict[str, Any]:
        """Extract equipment information from text."""
        equipment = {"zodiacs": None, "twins": None, "other": []}

        # Track confidence for each equipment type
        confidence = {"zodiacs": 0.0, "twins": 0.0}

        for pattern in self.equipment_patterns:
            matches = pattern.findall(text)
            for match in matches:
                try:
                    # Handle tuple or string match
                    if isinstance(match, tuple):
                        count_str = match[0]
                    else:
                        count_str = match

                    count = int(count_str)
                    pattern_str = pattern.pattern.lower()

                    # Only accept reasonable counts (1-20 is a reasonable range for expedition equipment)
                    if 1 <= count <= 20:
                        if "zodiac" in pattern_str:
                            # If we already have a value, only replace if new count has higher confidence
                            # or if the new count is more specific (e.g., "zodiac count: 7" vs "7 boats")
                            new_confidence = (
                                0.9
                                if "count" in pattern_str or ":" in pattern_str
                                else 0.8
                            )

                            if (
                                equipment["zodiacs"] is None
                                or new_confidence > confidence["zodiacs"]
                            ):
                                equipment["zodiacs"] = count
                                confidence["zodiacs"] = new_confidence

                        elif "twin" in pattern_str:
                            new_confidence = (
                                0.9
                                if "count" in pattern_str or ":" in pattern_str
                                else 0.8
                            )

                            if (
                                equipment["twins"] is None
                                or new_confidence > confidence["twins"]
                            ):
                                equipment["twins"] = count
                                confidence["twins"] = new_confidence

                        elif "boat" in pattern_str and "zodiac" not in pattern_str:
                            # Only count boats if they're not already counted as zodiacs
                            # and we don't have zodiacs specified elsewhere
                            if equipment["zodiacs"] is None:
                                equipment["zodiacs"] = count
                                confidence["zodiacs"] = (
                                    0.7  # Lower confidence for generic "boats"
                                )
                except (ValueError, IndexError):
                    continue

        # Look for other equipment mentions
        other_equipment_patterns = [
            (r"(\d+)\s+kayak[s]?", "kayaks"),
            (r"(\d+)\s+paddle[s]?", "paddles"),
            (r"(\d+)\s+life\s+jacket[s]?", "life jackets"),
            (r"(\d+)\s+radio[s]?", "radios"),
        ]

        for pattern, name in other_equipment_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    count = int(match)
                    if 1 <= count <= 100:  # Reasonable range
                        equipment["other"].append(f"{count} {name}")
                except (ValueError, IndexError):
                    continue

        # Convert None values to None (not 0) to indicate no data rather than zero equipment
        return equipment

    def _extract_weather(self, text: str) -> str:
        """Extract weather information from text."""
        weather_keywords = [
            "weather",
            "conditions",
            "wind",
            "rain",
            "sunny",
            "cloudy",
            "temperature",
        ]

        lines = text.split("\n")
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in weather_keywords):
                # Extract the relevant part of the line
                weather_info = line.strip()
                if len(weather_info) > 10 and len(weather_info) < 200:
                    return weather_info

        return ""

    def _extract_notes(self, text: str) -> str:
        """Extract operational notes from text."""
        # Look for sections that might contain notes
        note_keywords = ["notes", "observations", "remarks", "comments", "important"]

        lines = text.split("\n")
        notes_lines = []

        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in note_keywords):
                # Include this line and potentially the next few lines
                notes_lines.append(line.strip())

        if notes_lines:
            return " ".join(notes_lines)

        # If no specific notes section, return a summary of the first few lines
        summary_lines = [line.strip() for line in lines[:3] if line.strip()]
        return " ".join(summary_lines)[:200]  # Limit to 200 characters

    def _extract_personnel(self, text: str) -> Dict[str, Any]:
        """Extract personnel information from text."""
        personnel = {"total_count": None, "guides": [], "drivers": [], "staff": []}

        # First pass: Look for explicit personnel counts
        count_patterns = [
            r"(\d+)\s+(?:staff|personnel|guides|people)",
            r"staff\s+count\s*:\s*(\d+)",
            r"personnel\s+count\s*:\s*(\d+)",
            r"total\s+(?:staff|personnel|guides|people)\s*:\s*(\d+)",
        ]

        for pattern in count_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    count = int(match)
                    if 1 <= count <= 50:  # Reasonable range for expedition personnel
                        personnel["total_count"] = count
                        break
                except (ValueError, IndexError):
                    continue

        # Second pass: Look for guide/driver names
        # Look for names in specific contexts
        guide_patterns = [
            r"guide[s]?[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2})",
            r"([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2})\s+(?:is|will be|as)\s+(?:the\s+)?guide",
            r"([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2})\s+\(guide\)",
        ]

        driver_patterns = [
            r"driver[s]?[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2})",
            r"([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2})\s+(?:is|will be|as)\s+(?:the\s+)?driver",
            r"([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2})\s+\(driver\)",
        ]

        staff_patterns = [
            r"staff[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2})",
            r"([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2})\s+(?:is|will be|as)\s+(?:the\s+)?staff",
            r"([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2})\s+\(staff\)",
        ]

        # Extract guides
        for pattern in guide_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                name = match.strip()
                # Validate it looks like a proper name
                if (
                    len(name) > 2
                    and name[0].isupper()
                    and name not in personnel["guides"]
                ):
                    personnel["guides"].append(name)

        # Extract drivers
        for pattern in driver_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                name = match.strip()
                # Validate it looks like a proper name
                if (
                    len(name) > 2
                    and name[0].isupper()
                    and name not in personnel["drivers"]
                ):
                    personnel["drivers"].append(name)

        # Extract other staff
        for pattern in staff_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                name = match.strip()
                # Validate it looks like a proper name
                if (
                    len(name) > 2
                    and name[0].isupper()
                    and name not in personnel["staff"]
                ):
                    personnel["staff"].append(name)

        # Third pass: Look for names in lists or sections
        # This is more speculative, so we'll only do it if we haven't found names yet
        if not (personnel["guides"] or personnel["drivers"] or personnel["staff"]):
            # Look for sections that might contain personnel
            personnel_sections = re.findall(
                r"(?:staff|personnel|guides|team)[\s:]+([^\n]+(?:\n[^\n]+){0,5})",
                text,
                re.IGNORECASE,
            )

            for section in personnel_sections:
                # Look for capitalized words that might be names
                potential_names = re.findall(
                    r"([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2})", section
                )

                for name in potential_names:
                    # Skip common non-name words
                    if name.lower() in [
                        "the",
                        "and",
                        "with",
                        "for",
                        "expedition",
                        "guide",
                        "driver",
                        "staff",
                    ]:
                        continue

                    # Categorize based on context
                    if "guide" in section.lower() and name not in personnel["guides"]:
                        personnel["guides"].append(name)
                    elif (
                        "driver" in section.lower() and name not in personnel["drivers"]
                    ):
                        personnel["drivers"].append(name)
                    elif (
                        name not in personnel["staff"]
                        and name not in personnel["guides"]
                        and name not in personnel["drivers"]
                    ):
                        personnel["staff"].append(name)

        return personnel

    def _determine_operation_type(self, data: Dict[str, Any]) -> str:
        """Determine operation type based on extracted data."""
        arrival_time = data.get("arrival_time", "")
        departure_time = data.get("departure_time", "")

        if arrival_time and departure_time:
            try:
                arrival_hour = int(arrival_time.split(":")[0])
                departure_hour = int(departure_time.split(":")[0])

                if arrival_hour >= 12 and departure_hour >= 12:
                    return "pm_only"
                elif arrival_hour < 12 and departure_hour < 12:
                    return "am_only"
                else:
                    return "combined"
            except (ValueError, IndexError):
                pass

        return "combined"
